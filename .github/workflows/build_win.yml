name: Build Windows
on: 
  workflow_dispatch:
jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install Python Dependencies
      run: |
        cd backend
        pip install pyinstaller
        pip install -r requirements.txt

    - name: Build Python Backend
      run: |
        cd backend
        pyinstaller --onefile --name fastapi_app --add-data="app:app" -p . app/main.py

    - name: Install Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20

    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        target: x86_64-pc-windows-msvc
        profile: minimal
        override: true

    - name: Install npm dependency
      run: npm install
      
    - name: Install Tauri CLI
      run: npm install @tauri-apps/cli

    - name: Build Tauri
      run: npm run tauri build -- --target x86_64-pc-windows-msvc
      env:
        CI: true

    - name: Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: windows-build
        path: |
          src-tauri/target/x86_64-pc-windows-msvc/release/bundle/msi/*.msi
          src-tauri/target/x86_64-pc-windows-msvc/release/bundle/nsis/*.exe
