# Melodyze Reaper Assistant

### Install NodeJS

### Install Typescript

### Install Rust

### Install Tauri CLI

### Install Python

### Install Python Dependencies & UV

### Install PyInstaller

- Mac

```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

rustc --version
cargo --version

To install Cargo dependencies for a Rust project, use:

```bash
cd /style-manager-app/src-tauri
cargo build
```

### NPM Install

1. /style-manager-app `npm install`

### Cargo Install

# Backend

- This modularized FastAPI App
- Backend path is: /style-manager-app/backend
- All backend code should inside: /style-manager-app/backend/app
- Run backend:
  1. Go to: /style-manager-app/backend
  2. Run `uvicorn app.main:app --reload`

## Build Backend Dist - Windows/MAC/Linux - Final

```bash
pyinstaller ./fastapi_app.spec
```

## Build Backend Dist - Windows

```bash
pyinstaller --onefile --name fastapi_app --add-data="app:app" -p . app\\main.py
```

- In fastapi_app.spec use app\\main.py
  tauri.conf.json


## Build Backend Dist - Mac/Linux

```bash
pyinstaller --onefile --name fastapi_app --add-data="app:app" -p . app/main.py
```

- In fastapi_app.spec use app/main.py
  tauri.conf.json
```

## Run Project

- Dev

```bash
npm run tauri dev
```

- Release

```bash
npm run tauri build
npm run tauri build -- --debug
```

- Backend build
- Frontend build
- Cargo Build
- Tauri Build

### Similar AI Project - [Transport](https://github.com/iam-veeramalla/transport)

<!-- Next Action of task for this project:
- Enable Auth
- Enable User Permission Management
- Replace all Input files to absolute path
- Remove all backend credentials
- Convert python Backend to offline mode
- Enable Versioning
- Automatic app distribution from a artifact
- Manual update screen
- Enable Auto update 
- Secure build (To avoid reverse engineering)
 -->