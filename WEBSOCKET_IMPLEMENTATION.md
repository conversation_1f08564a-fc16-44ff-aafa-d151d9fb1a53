# WebSocket Implementation Guide

This document provides an overview of the WebSocket implementation for real-time communication between the client and the Python backend in the Melodyze Reaper Assistant.

## Overview

The WebSocket implementation enables:

1. Real-time status updates from nested functions to the client
2. Server-initiated data transmission to the UI without API calls
3. Reduced API timeouts and improved performance for long-running operations

## Backend Implementation

### Key Components

1. **ConnectionManager** (`websocket_manager.py`)
   - Manages WebSocket connections
   - Handles message broadcasting and personal messages
   - Provides a singleton instance for application-wide access

2. **WebSocket Router** (`websocket_router.py`)
   - Defines WebSocket endpoints
   - Handles client connections and message routing
   - Processes chat messages, file uploads, and reset requests

3. **WebSocket Utils** (`ai_assistant/utilities/websocket_utils.py`)
   - Provides utility functions for sending status updates
   - Includes both async and sync versions for different contexts

### Integration with Existing Code

- The chat completion functions now accept a `client_id` parameter for status updates
- Tool functions are wrapped with WebSocket status update capabilities
- The main FastAPI app includes the WebSocket router

## Frontend Implementation

### Key Components

1. **WebSocketService** (`websocketService.ts`)
   - Manages WebSocket connections
   - Provides methods for sending chat messages, uploading files, and resetting chat
   - Handles reconnection logic and message routing

2. **AIAssistantChat Component** (`AIAssistantChat.tsx`)
   - Uses WebSocket for real-time communication
   - Falls back to REST API if WebSocket is not connected
   - Displays status messages from the server

## Message Types

### Client to Server

- `chat_message`: Send a chat message to the AI assistant
- `file_upload`: Upload a file for analysis
- `reset`: Reset the chat conversation

### Server to Client

- `connection_status`: Connection status updates
- `chat_response`: Response to a chat message
- `file_upload_response`: Response to a file upload
- `reset_response`: Response to a reset request
- `status_update`: Real-time status updates from the server
- `tool_execution`: Updates about tool execution
- `error`: Error messages

## Usage Examples

### Sending Status Updates from Nested Functions

```python
# In any Python function
from app.utilities.websocket_utils import send_status_update_sync

def some_nested_function(client_id):
    # Send a status update
    send_status_update_sync(
        client_id,
        "processing",
        "Processing your request...",
        {"progress": 50}
    )
    
    # Do some work...
    
    # Send another status update
    send_status_update_sync(
        client_id,
        "complete",
        "Processing complete!",
        {"progress": 100}
    )
```

### Listening for Status Updates in the Frontend

```typescript
// In a React component
import websocketService from '@/utils/websocketService';

useEffect(() => {
  // Register a handler for status updates
  const unsubscribe = websocketService.onMessage('status_update', (message) => {
    console.log('Status update:', message.message);
    // Update UI based on status
  });
  
  // Cleanup
  return () => {
    unsubscribe();
  };
}, []);
```

## Future Enhancements

1. Add authentication to WebSocket connections
2. Implement message queuing for offline scenarios
3. Add support for binary data transmission (e.g., for file uploads)
4. Migrate more REST API endpoints to WebSocket

## Troubleshooting

- If WebSocket connections fail, the system will automatically fall back to REST API calls
- Check the status messages panel in the UI for connection status and errors
- WebSocket connections will automatically attempt to reconnect if disconnected