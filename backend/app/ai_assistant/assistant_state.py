# assistant_state.py
from typing import List, Dict, Optional
import os
from app.ai_assistant.rpp_parser import rpp_parser


def summarize_rpp_project(rpp_root, max_tracks=50):
    """Generate a summary of a REAPER project from its parsed RPP tree."""
    lines = []
    # Global info
    tempo_elem = rpp_root.find(".//TEMPO")
    if tempo_elem:
        bpm = tempo_elem[1]
        ts_num = tempo_elem[2] if len(tempo_elem) > 2 else "4"
        ts_den = tempo_elem[3] if len(tempo_elem) > 3 else "4"
        lines.append(f"Tempo: {bpm} BPM, Time Signature: {ts_num}/{ts_den}")
    # Project markers
    markers = [
        child
        for child in rpp_root.children
        if isinstance(child, list) and child[0] == "MARKER"
    ]
    if markers:
        marker_summaries = []
        for mark in markers:
            # Assuming format: [ 'MARKER', index, position, "Name", color, ... flags ... ]
            idx = mark[1]
            pos = mark[2]
            name = mark[3].strip('"')
            # Check if it's a region (if a flag indicating region is present, e.g. a "1")
            is_region = False
            if len(mark) > 5 and mark[5] == "1":
                is_region = True
            if is_region:
                marker_summaries.append(f'Region "{name}" start at {pos}')
            else:
                marker_summaries.append(f'Marker {idx} @ {pos}s: "{name}"')
        lines.append("Markers/Regions: " + "; ".join(marker_summaries))
    # Master track settings
    master_vol = rpp_root.find(".//MASTER_VOLUME")
    if master_vol:
        vol_val = float(master_vol[1])
        pan_val = float(master_vol[2])
        # Format volume in dB
        db = (20 * __import__("math").log10(vol_val)) if vol_val > 0 else float("-inf")
        pan_text = (
            "Center"
            if pan_val == 0
            else f"{abs(int(pan_val * 100))}% {'Left' if pan_val < 0 else 'Right'}"
        )
        vol_text = f"{vol_val:.2f} ({db:+.1f} dB)" if vol_val > 0 else "-∞ dB"
        lines.append(f"Master Volume: {vol_text}, Pan: {pan_text}")
    master_mute = rpp_root.find(".//MASTERMUTESOLO")
    if master_mute:
        # Only one value (mute) typically for master
        if master_mute[1] == "1":
            lines.append("Master Track is MUTED")
        # (Master solo usually not applicable, can skip)
    master_fx = rpp_root.find(".//MASTER_FX")
    if master_fx and master_fx[1] == "0":
        lines.append("Master FX Bypassed (offline)")
    # Project notes
    notes_elem = rpp_root.find(".//NOTES")
    if notes_elem:
        # If notes text is stored as children or data:
        note_text = ""
        for child in notes_elem:
            if isinstance(child, str):
                note_text += child + "\n"
        note_text = note_text.strip()
        if note_text:
            lines.append(f'Project Notes: "{note_text}"')
    # Track details
    track_elems = rpp_root.findall(".//TRACK")[:max_tracks]
    for t_index, track in enumerate(track_elems, start=1):
        # Basic track header with name
        name_elem = track.find("NAME")
        track_name = name_elem[1] if name_elem and len(name_elem) > 1 else ""
        track_name = track_name.strip('"')
        track_name = track_name if track_name else "[untitled]"
        mute_elem = track.find("MUTESOLO")
        mute = solo = False
        solo_safe = False
        if mute_elem:
            # MUTESOLO typically has three values: mute, solo, flags
            mute = mute_elem[1] == "1"
            solo = len(mute_elem) > 2 and mute_elem[2] == "1"
            if len(mute_elem) > 3:
                solo_safe = mute_elem[3] == "1"
        status = []
        if mute:
            status.append("Muted")
        if solo:
            status.append("Soloed")
        if solo_safe:
            status.append("Solo Safe")
        status_text = ", ".join(status) if status else "Normal"
        lines.append(f'\nTrack {t_index}: "{track_name}" ({status_text})')
        # Volume/Pan
        volpan_elem = track.find("VOLPAN")
        if volpan_elem:
            vol_val = float(volpan_elem[1])
            pan_val = float(volpan_elem[2])
            db = (
                (20 * __import__("math").log10(vol_val))
                if vol_val > 0
                else float("-inf")
            )
            pan_text = (
                "Center"
                if pan_val == 0
                else f"{abs(int(pan_val * 100))}% {'Left' if pan_val < 0 else 'Right'}"
            )
            vol_text = f"{vol_val:.2f} ({db:+.1f} dB)" if vol_val > 0 else "-∞ dB"
            lines.append(f"  Volume: {vol_text}, Pan: {pan_text}")
        # FX count
        fxchain = track.find("FXCHAIN")
        if fxchain:
            # Count plugin entries in FXCHAIN
            fx_count = 0
            for fx in fxchain.children:
                if isinstance(fx, list):
                    # older rpp parser might give VST lines as list or element
                    if fx and fx[0] in ("VST", "AU", "JS", "DX"):
                        fx_count += 1
                else:
                    # If fx is Element (perhaps rppxml style), check tag
                    if getattr(fx, "tag", "") in ("VST", "AU", "JS", "DX"):
                        fx_count += 1
            lines.append(
                f"  FX Chain: {fx_count} plugin(s)"
                if fx_count
                else "  FX Chain: (empty)"
            )
        else:
            lines.append("  FX Chain: (none)")
        # Track color (PEAKCOL)
        peakcol = track.find("PEAKCOL")
        if peakcol:
            color_val = peakcol[1]
            lines.append(f"  Color: {color_val}")
        # Routing: Master send and other sends/receives
        mainsend = track.find("MAINSEND")
        if mainsend and mainsend[1] == "0":
            lines.append("  (Master/Parent Send disabled)")
        # Gather sends from this track (AUXSEND lines if any)
        sends = [
            ch for ch in track.children if isinstance(ch, list) and ch[0] == "AUXSEND"
        ]
        recvs = [
            ch for ch in track.children if isinstance(ch, list) and ch[0] == "AUXRECV"
        ]
        if sends:
            send_list = []
            for s in sends:
                # Format: ['AUXSEND', dest_track_index, src_chan, dest_chan, volume, pan, etc...]
                dest_idx = s[1]
                send_vol = s[5] if len(s) > 5 else None
                txt = f"Track {dest_idx}"
                if send_vol:
                    txt += f" (vol {send_vol})"
                send_list.append(txt)
            lines.append("  Sends to: " + ", ".join(send_list))
        if recvs:
            recv_list = []
            for r in recvs:
                src_idx = r[1]
                recv_vol = r[5] if len(r) > 5 else None
                txt = f"Track {src_idx}"
                if recv_vol:
                    txt += f" (vol {recv_vol})"
                recv_list.append(txt)
            lines.append("  Receives from: " + ", ".join(recv_list))
        if not sends and not recvs:
            lines.append("  Sends/Receives: none")
        # Track notes
        t_notes = track.find("NOTES")
        if t_notes:
            note_text = ""
            for child in t_notes:
                if isinstance(child, str):
                    note_text += child + "\n"
            note_text = note_text.strip()
            lines.append(f'  Notes: "{note_text}"' if note_text else "  Notes: (empty)")
    # If there are more tracks not summarized:
    total_tracks = len(rpp_root.findall(".//TRACK"))
    if total_tracks > max_tracks:
        lines.append(f"\n(+ {total_tracks - max_tracks} more tracks not shown)")
    return "\n".join(lines)


class AssistantState:
    _instance: Optional['AssistantState'] = None

    def __init__(self):
        if AssistantState._instance is not None:
            raise Exception("AssistantState:Singleton!")
        self._reset()
        AssistantState._instance = self

    @staticmethod
    def get() -> 'AssistantState':
        """Get the singleton instance. Creates one if it doesn't exist."""
        if AssistantState._instance is None:
            AssistantState()
        assert AssistantState._instance is not None
        return AssistantState._instance  # This is guaranteed to not be None

    # ---------- helpers ----------
    def _reset(self):
        self.file_path = None
        self.conversation_history: List[Dict] = []
        self.project_summary = ""

    # -------- helpers --------
    def load_project(self, file_path: str):
        filename_only = os.path.splitext(os.path.basename(file_path))[0]
        rpp_parser.parse(file_path)
        self.project_summary = rpp_parser.parsed_content
        print("Project Parsed")
        system_prompt = (
            "You are Reaper's DAW assistant.\n"
            "- To gather metadata information like Song Name, Artist Name, Genre, Key, Scale, check the filename first.\n"
            "- Filename may contain song name along with artist name, project genre, key, and scale.\n"
            "- If not available then suggest options or ask the user.\n"
            "- Represent Scale in Major/Minor. Represent Key/Pitch in A, A#, B, C, C#, D, D#, E, F, F#, G, G#.\n"
            "- **When you need to confirm parameters for ANY tool, ask ONLY for that tool's"
            "   arguments and do NOT bring up unrelated project metadata (song name, artist, key, scale, etc.).** "
            "- After the user confirms (or supplies) all required arguments then proceed with the action.\n"
            f"Filename: **{filename_only}**"
        )
        self.conversation_history = [
            {"role": "system", "content": system_prompt},
            {
                "role": "assistant",
                "content": f"Project analysis completed.\n Please Let me know how can I help you in **{filename_only}** ?",
            },
        ]

        from app.ai_assistant.tool_impl import TOOL_IMPL
        from reapy import open_project, Project

        open_project(file_path)
        project = Project()
        tool_impl = TOOL_IMPL.init(project)
        tool_impl.reset_instance(project)

    def discard(self):
        self._reset()


# TODO LIST:
# - Project summary should be updated after each tool call
# - Should save the project on tool call because to parse it again from filepath need updated file
# - When user changes the project manually How manage latest update on project summary
# - Project summary optimization
# - Vetor DB for project summary
# - if project connected  with reaper on app close reaper also getting closed
