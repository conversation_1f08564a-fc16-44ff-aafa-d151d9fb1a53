from openai import OpenAI
from dotenv import load_dotenv
from app.env import env
from app.ai_assistant.tool_defs import tools_defs
from app.utilities.websocket_utils import send_status_update_sync, send_streaming_update
load_dotenv()

client = OpenAI(api_key=env.OPENAI_API_KEY)


# def get_response(messages):
#     response = client.chat.completions.create(
#         model="gpt-4.1-mini",
#         messages=messages,
#         temperature=0,
#         response_format={"type": "json_object"},
#     )
#     return response.choices[0].message.content


async def chat_completion_function_stream(conversation_history, client_id=None):
    """Stream the chat completion with function calling support"""
    # Send status update if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "processing",
            "Analyzing your request with AI..."
        )
    
    # Create streaming response
    stream = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=conversation_history,
        tools=tools_defs,
        tool_choice="auto",
        parallel_tool_calls=True,
        stream=True
    )
    
    # Initialize variables to collect streaming data
    collected_message = {
        "role": "assistant",
        "content": ""
    }
    
    # For collecting function calls
    function_calls = []
    # current_function_call = None
    
    # Process the stream
    for chunk in stream:
        delta = chunk.choices[0].delta
        
        # Handle content updates
        if delta.content:
            collected_message["content"] += delta.content
            # Send content update to client
            if client_id:
                await send_streaming_update(client_id, "content_chunk", delta.content)
        
        # Handle function call updates
        if delta.tool_calls:
            for tool_call in delta.tool_calls:
                # Get the index of this tool call
                index = tool_call.index
                
                # Initialize function call if needed
                while len(function_calls) <= index:
                    function_calls.append({
                        "id": "",
                        "type": "function",
                        "function": {
                            "name": "",
                            "arguments": ""
                        }
                    })
                
                # Update function call data
                if tool_call.id:
                    function_calls[index]["id"] = tool_call.id
                
                if tool_call.function:
                    if tool_call.function.name:
                        function_calls[index]["function"]["name"] = tool_call.function.name
                        # Send tool call name update to client
                        if client_id:
                            await send_streaming_update(
                                client_id, 
                                "tool_call_name", 
                                tool_call.function.name,
                                {"index": index}
                            )
                    
                    if tool_call.function.arguments:
                        function_calls[index]["function"]["arguments"] += tool_call.function.arguments
                        # Send tool call argument update to client
                        if client_id:
                            await send_streaming_update(
                                client_id, 
                                "tool_call_args", 
                                tool_call.function.arguments,
                                {"index": index, "name": function_calls[index]["function"]["name"]}
                            )
    
    # Add function calls to the message if any were made
    if function_calls and any(fc["function"]["name"] for fc in function_calls):
        collected_message["tool_calls"] = function_calls
    
    # Send completion status if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "ai_response_received",
            "AI response received, processing next steps..."
        )
    
    # Convert to OpenAI message format
    from openai.types.chat import ChatCompletionMessage
    return ChatCompletionMessage.model_validate(collected_message)


async def chat_completion_stream(conversation_history, client_id=None):
    """Stream the chat completion without function calling"""
    # Send status update if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "processing",
            "Generating final response..."
        )
    
    # Create streaming response
    stream = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=conversation_history,
        stream=True
    )
    
    # Initialize variables to collect streaming data
    collected_message = {
        "role": "assistant",
        "content": ""
    }
    
    # Process the stream
    for chunk in stream:
        delta = chunk.choices[0].delta
        
        # Handle content updates
        if delta.content:
            collected_message["content"] += delta.content
            # Send content update to client
            if client_id:
                await send_streaming_update(client_id, "content_chunk", delta.content)
    
    # Send completion status if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "ai_response_complete",
            "Response ready"
        )
    
    # Convert to OpenAI message format
    from openai.types.chat import ChatCompletionMessage
    return ChatCompletionMessage.model_validate(collected_message)


# Non-streaming versions for backward compatibility
def chat_completion_function(conversation_history, client_id=None):
    # Send status update if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "processing",
            "Analyzing your request with AI..."
        )
    
    response = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=conversation_history,
        tools=tools_defs,
        tool_choice="auto",
        parallel_tool_calls=True
    )
    
    # Send completion status if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "ai_response_received",
            "AI response received, processing next steps..."
        )
    
    return response.choices[0].message


def chat_completion(conversation_history, client_id=None):
    # Send status update if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "processing",
            "Generating final response..."
        )
    
    response = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=conversation_history,
    )
    
    # Send completion status if client_id is provided
    if client_id:
        send_status_update_sync(
            client_id,
            "ai_response_complete",
            "Response ready"
        )
    
    return response.choices[0].message
