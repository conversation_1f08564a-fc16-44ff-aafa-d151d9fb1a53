import uuid
import rpp
import re
from typing import Dict, List, Any, Tuple, Union

INPUT_RPP = "/Users/<USER>/melodyze-org/ReaperAssistant/data/hey jude rock.RPP"
MIN_COMBINED_LENGTH = 100


class RppParser:
    def __init__(self):
        self.parsed_content = ""
    
    def _extract_base64(self, el: rpp.Element):
        """
        Inspect el.children; if they're ALL strings and long enough, pull them out.
        Otherwise recurse into any child Elements.
        """
        if el.children and all(isinstance(c, str) for c in el.children):
            total_len = sum(len(c) for c in el.children)
            if el.children and total_len >= MIN_COMBINED_LENGTH and all(isinstance(c, str) and not c.strip().startswith("<") and re.match(r'^[A-Za-z0-9+/=]+$', c.strip()) for c in el.children):
                uid = uuid.uuid4().hex + ".b64"
                el.children = [f"[${uid}]"]
                return

        for child in el.children:
            if isinstance(child, rpp.Element):
                self._extract_base64(child)

    def _extract_and_replace_midi_events(self, data: List[Union[List[str], object]]) -> Tuple[List[List[str]], List[Union[List[str], object]]]:
        extracted_events = []
        replaced_data = []
        item_guid = uuid.uuid4().hex

        for item in data:
            if isinstance(item, list) and item and item[0] == 'E':
                if not extracted_events:
                    replaced_data.append([[f'[#{item_guid}.midi]']])
                extracted_events.append(item)
            elif isinstance(item, list) and item and item[0] == 'E' and extracted_events:
                continue
            elif isinstance(item, list) and item[0] == f'[#{item_guid}.midi]':
                continue
            else:
                replaced_data.append(item)

        return extracted_events, replaced_data, item_guid

    def _extract_midi_events(self, project) -> Dict[str, List[Dict[str, Any]]]:
        for track in project.findall("TRACK"):
            for item in track.findall("ITEM"):

                source_tags = item.findall("SOURCE")
                if not source_tags:
                    continue

                for source in source_tags:
                    if source.attrib and source.attrib[0].strip().upper() == "MIDI":
                        extracted_events, replaced_data, item_guid = self._extract_and_replace_midi_events(
                            source.children)
                        source.children = replaced_data

    def parse(self, input_rpp: str):
        with open(input_rpp, "r") as f:
            project = rpp.load(f)
        self._extract_base64(project)
        self._extract_midi_events(project)
        self.parsed_content = str(project)

rpp_parser = RppParser()

if __name__ == "__main__":
    rpp_parser.parse(INPUT_RPP)
