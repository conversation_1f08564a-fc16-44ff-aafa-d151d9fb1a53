# Define the OpenAI function schemas for the available tools
tools_defs = [
    {
        "type": "function",
        "function": {
            "name": "list_tracks",
            "strict": True,
            "description": (
                "List all tracks in the DAW"
            ),
            "parameters": {
                "type": "object",
                "properties": {},
                "required": [],
                "additionalProperties": False
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mute_track",
            "strict": True,
            "description": (
                "Mute a track in the DAW by track number"
                "if user only provide track name (e.g 'Drums') then you need to find the track number"
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "track_number": {
                        "type": "integer",
                        "description": "Number of the track to mute (e.g., 1, 2)."
                    }
                },
                "required": ["track_number"],
                "additionalProperties": False
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "solo_track",
            "strict": True,
            "description": (
                "Solo a track in the DAW by track number"
                "if user only provide track name (e.g 'Drums') then you need to find the track number"
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "track_number": {
                        "type": "integer",
                        "description": "Number of the track to solo (e.g., 1, 2)."
                    }
                },
                "required": ["track_number"],
                "additionalProperties": False
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "twelve_scale_render",
            "strict": True,
            "description": (
                "Render audio in 12 different musical keys around a default key."
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "default_key": {"type": "string", "description": "Root key (e.g. C)"},
                    "up_shift": {"type": "integer", "default": 6,  "description": "semitones above, range is 0 to 11"},
                    "down_shift": {"type": "integer", "default": -5, "description": "semitones below, range is -1 to -12"},
                    "save_12_session": {"type": "boolean", "default": True, "description": "Save session?"},
                    "total_wait_time": {"type": "integer", "default": 30, "description": "Wait time in seconds, can be given by the user in flexible formats like '1 min'"}
                },
                "required": ["default_key", "up_shift", "down_shift", "save_12_session", "total_wait_time"],
                "additionalProperties": False
            }
        }
    }
]
