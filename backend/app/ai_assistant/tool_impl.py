from reapy import Project

class TOOL_IMPL:
    _instance = None

    def __init__(self, project: Project):
        if TOOL_IMPL._instance is not None:
            raise Exception("TOOL_IMPL:Singleton!")
        self.project: Project = project
        TOOL_IMPL._instance = self

    @staticmethod
    def init(project):
        if TOOL_IMPL._instance is None:
            TOOL_IMPL(project)
            print("TOOL_IMPL:get", project.name, project)
            assert TOOL_IMPL._instance is not None
        return TOOL_IMPL._instance
    
    def reset_instance(self, project: Project):
        self.project = project
        print("TOOL_IMPL:reset_instance", self.project.name, self.project)

    def mute_track(self, track_number):
        try:
            if track_number < 1 or track_number > len(self.project.tracks):
                return f"Track {track_number} doesn't exist"
            
            track = self.project.tracks[track_number - 1]
            track.mute()
            return f"Muted track {track_number}: '{track.name}'"
            
        except Exception as e:
            raise e
    
    def solo_track(self, track_number):
        try:
            if track_number < 1 or track_number > len(self.project.tracks):
                return f"Track {track_number} doesn't exist"
            
            track = self.project.tracks[track_number - 1]
            track.solo()
            return f"Soloed track {track_number}: '{track.name}'"
            
        except Exception as e:
            raise e
    
    def list_tracks(self):
        try:
            tracks_info = []
            for i, track in enumerate(self.project.tracks, 1):
                status = []
                if track.is_muted:
                    status.append("MUTED")
                if track.is_solo:
                    status.append("SOLO")
                
                status_str = f" [{', '.join(status)}]" if status else ""
                tracks_info.append(f"{i}. {track.name}{status_str}")
            
            return "Tracks in project:\n" + "\n".join(tracks_info)
            
        except Exception as e:
            raise e

    def twelve_scale_render(self,default_key: str, up_shift: int, down_shift: int, 
                            save_12_session: bool, total_wait_time: int):
        try:
            return (
            f"Rendered audio in 12 keys around '{default_key}' (up {up_shift}, down {down_shift}). "
            f"Saved session: {save_12_session}. Wait time: {total_wait_time} seconds."
        )
        except Exception as e:
            raise e