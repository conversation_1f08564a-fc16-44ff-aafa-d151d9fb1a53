# tool_registry.py

"""Map OpenAI tool names → bound callables.
   This is created *after* the user loads a project so that every
   callable already has access to the live `reapy.Project` via TOOL_IMPL.
"""
from typing import Callable, Dict, Optional
from functools import wraps
from app.utilities.websocket_utils import send_status_update_sync

def with_websocket_updates(func):
    """Decorator to add WebSocket status updates to tool functions"""
    @wraps(func)
    def wrapper(*args, client_id=None, **kwargs):
        # Extract function name for status updates
        func_name = func.__name__
        display_name = func_name.replace('_', ' ').title()
        
        # Send starting status if client_id is provided
        if client_id:
            send_status_update_sync(
                client_id,
                "tool_execution",
                f"Starting {display_name}...",
                {"tool": func_name, "status": "starting"}
            )
        
        try:
            # Execute the original function
            result = func(*args, **kwargs)
            
            # Send success status if client_id is provided
            if client_id:
                send_status_update_sync(
                    client_id,
                    "tool_execution",
                    f"{display_name} completed successfully",
                    {"tool": func_name, "status": "completed"}
                )
            
            return result
            
        except Exception as e:
            # Send error status if client_id is provided
            if client_id:
                send_status_update_sync(
                    client_id,
                    "tool_execution",
                    f"Error in {display_name}: {str(e)}",
                    {"tool": func_name, "status": "error", "error": str(e)}
                )
            
            # Re-raise the exception
            raise
    
    return wrapper

def build_tool_map(client_id: Optional[str] = None) -> Dict[str, Callable]:
    """Return the current tool-name → function map with optional WebSocket updates."""
    from app.ai_assistant.tool_impl import TOOL_IMPL
    tool_impl = TOOL_IMPL.init(None)
    
    # Apply the WebSocket update decorator to each tool function
    if getattr(build_tool_map, "_cache", None) is None:
        # Create a dictionary of tool functions with the decorator applied
        tool_functions = {}
        
        # Define the original tool functions
        original_functions = {
            "mute_track": tool_impl.mute_track,
            "solo_track": tool_impl.solo_track,
            "list_tracks": tool_impl.list_tracks,
            "twelve_scale_render": tool_impl.twelve_scale_render,
        }
        
        # Apply the decorator to each function
        for name, func in original_functions.items():
            # Create a wrapper that includes the client_id
            @wraps(func)
            def create_tool_fn(func=func):
                def tool_fn(*args, **kwargs):
                    # Add the client_id to the kwargs if provided
                    if client_id:
                        kwargs['client_id'] = client_id
                    return with_websocket_updates(func)(*args, **kwargs)
                return tool_fn
            
            tool_functions[name] = create_tool_fn()
        
        build_tool_map._cache = tool_functions
    
    return build_tool_map._cache


# def build_tool_map() -> Dict[str, Callable]:
#     """Return the current tool-name → function map.
#     Should be called right after AssistantState.load_project()."""
#     from app.ai_assistant.tool_impl import TOOL_IMPL

#     impl = TOOL_IMPL.get(None)
#     print("impl.project: ", impl.project)
#     return {
#         "mute_track": impl.mute_track,
#         "solo_track": impl.solo_track,
#         "list_tracks": impl.list_tracks,

#         "twelve_scale_render": impl.twelve_scale_render,
#     }

