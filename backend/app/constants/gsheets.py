import requests


class Gsheet:
    @staticmethod
    def get_vst_range_list():
        response = requests.get(
            "https://script.google.com/macros/s/AKfycbxvG_PK1ZRadj0lRx1SSRo5NJIm4dkIRYurWPMAZYTQ4_2g9W6BEDcioOAa3RWjVifa/exec"
        )
        return response.json()

    @staticmethod
    def get_non_transposable_keywords():
        response = requests.get(
            "https://script.google.com/macros/s/AKfycbxcLC2m0yVC1-wPkLnIyn4vubuyzi5BACN3-IVUGTZWuXWL5IhftS4IctxfGStkUfE/exec"
        )
        return response.json()

    @staticmethod
    def get_note_name_no():
        response = requests.get(
            "https://script.google.com/macros/s/AKfycbxMj5EfzH00zppmHbei8ntrhaNGKcPzPPXmBw-JShrsZlwU2J0SOD2wIMM6FbG9uETb_w/exec"
        )
        return response.json()

    @staticmethod
    def get_chord_root():
        response = requests.get(
            "https://script.google.com/macros/s/AKfycbyO0jDivhOIStFsKrL2OXDZlJYD_3CjTOxpPSpztXxSyPY9UYCkCfIqqSnrzGpvmhxLhA/exec"
        )
        return response.json()

    @staticmethod
    def get_chord_note_offset_short():
        response = requests.get(
            "https://script.google.com/macros/s/AKfycbxiBqar59BYFdOo4MLIVv8rh1ScndeG-91UhrSrZYVVcNUIVazHap67Xa9pl0pVIDQ5/exec"
        )
        return response.json()

    @staticmethod
    def get_marker_list():
        response = requests.get("")
        return response.json()
