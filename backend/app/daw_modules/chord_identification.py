from app.constants.gsheets import Gsheet
import time
from app.utilities.time_utils import TimeUtils
from app.utilities.commons import Commons

class ChordIdentifier:
    def __init__(self, time_sig, input_midi, input_key, aligned_midi, section_list):
        self.time_sig = time_sig
        self.input_midi = input_midi
        self.input_key = input_key
        self.aligned_midi = aligned_midi
        self.section_list = section_list
        self.note_name_no = Gsheet.get_note_name_no()
        self.chord_note_offset_short = Gsheet.get_chord_note_offset_short()


    def merge_tracks(self, mid, rpp_file):        
        """Merge tracks while preserving the order based on absolute time."""
        import reapy
        import reapy.reascript_api as RPR
        messages = []

        RPR.Main_openProject(rpp_file) # type: ignore
        time.sleep(5) # vst loading time

        project = reapy.Project()
        vst_range_df = Gsheet.get_vst_range_list()
        non_transposable_keywords = Gsheet.get_non_transposable_keywords()
        # note_name_no = Gsheet.get_note_name_no()

        track_no = 0
        mido_track_name_list = []
        for track in mid.tracks:
            track_name = track.name if track.name else "Unnamed Track"
            while (track_name in mido_track_name_list):
                track_name = track_name + "_"
            mido_track_name_list.append(track_name)

            rpr_track_name_list = []
            for i, rpr_track in enumerate(project.tracks):
                rpr_track_name = rpr_track.name
                # if track name same then make it unique
                while (rpr_track_name in rpr_track_name_list):
                    rpr_track_name = rpr_track_name + "_"
                rpr_track_name_list.append(rpr_track_name)

                if not track_name == rpr_track_name:
                    continue

                transposable = True
                for keyword in non_transposable_keywords:
                    if Commons.search_word_in_string(keyword, track_name.lower()):
                        transposable = False
                        break

                if not transposable:
                    break

                this_allowed_range = []
                num_fx = RPR.TrackFX_GetCount(rpr_track.id)  # type: ignore
                for index, row in enumerate(vst_range_df):  #.iterrows():
                    if index == 0:
                        continue
                    vst_name_range = str(row[0])
                    low = int(self.note_name_no[str(row[1]).upper()])
                    high = int(self.note_name_no[str(row[2]).upper()])

                    if Commons.search_word_in_string(vst_name_range.lower(), rpr_track_name.lower()):
                        this_allowed_range.append((low, high))
                        continue

                    for fx_index in range(num_fx):
                        vst_name_current_track: str = RPR.TrackFX_GetFXName(rpr_track.id, fx_index, "", 1024)[3] # type: ignore
                        if Commons.search_word_in_string(vst_name_range.lower(), vst_name_current_track.lower()):
                            this_allowed_range.append((low, high))

                if len(this_allowed_range) == 0:
                    this_allowed_range.append((0, 999))

                
                abs_time = 0
                prev_time = 0
                for msg in track:
                    abs_time += msg.time
                    if msg.type == 'note_on' or msg.type == 'note_off':
                        is_transposable_note = False
                        for allowed_range in this_allowed_range:
                            if msg.note >= allowed_range[0] and msg.note <= allowed_range[1]:
                                is_transposable_note = True
                                break
                        if is_transposable_note:
                            messages.append((msg.copy(time=abs_time-prev_time), track_no))
                            prev_time = abs_time
                    else: 
                        messages.append((msg.copy(time=abs_time-prev_time), track_no))
                        prev_time = abs_time
                track_no += 1

        return messages

    def get_starting_notes_for_each_interval(
        self, messages, ticks_per_beat, interval_in_beats
    ):
        beat_ticks = int(ticks_per_beat * interval_in_beats)
        smallest_note_duration = ticks_per_beat // 32

        cumulative_time = 0

        current_interval_start = 0
        current_interval_end = beat_ticks

        # Dictionary to track when each note started
        note_start_times = {}
        # Dictionary to keep note durations: {note: duration}
        note_durations = {}
        # Contains tuples of (note, duration)
        notes_in_current_interval = dict()
        note_to_interval = {}

        notes_per_interval = {}

        for (msg, track_no) in messages:

            cumulative_time += msg.time
            # Before processing the message, store the notes that started in the previous interval
            if cumulative_time >= current_interval_end:
                notes_per_interval[current_interval_start] = (
                    notes_in_current_interval.copy()
                )
                current_interval_start = current_interval_end
                current_interval_end += beat_ticks
                notes_in_current_interval.clear()

            note_track = ""
            if msg.type == "note_on" or msg.type == "note_off":
                note_track = str(msg.note) + "-" + str(track_no)

            type_ = msg.type

            # If one same note appears twice with note_on message without a note_off in between then the second note_on is treated as note_off
            if msg.type == "note_on" and note_track in note_start_times:
                type_ = "note_off"

            if type_ == "note_on":
                note_start_times[note_track] = cumulative_time
                note_to_interval[note_track] = current_interval_start
            elif type_ == "note_off":
                #             print("Off:"+str(note_track))
                if note_track in note_start_times:
                    note_duration = cumulative_time - note_start_times[note_track]
                    note_durations[note_track] = note_duration

                    # If the note started in the current interval, add it to the current notes
                    if (
                        note_start_times[note_track] < current_interval_end
                        and note_start_times[note_track] >= current_interval_start
                    ):
                        if note_durations[note_track] >= smallest_note_duration:
                            notes_in_current_interval[note_track] = (
                                note_start_times[note_track] - current_interval_start,
                                cumulative_time - current_interval_start,
                            )

                    elif note_start_times[note_track] < current_interval_end:
                        if note_durations[note_track] >= smallest_note_duration:
                            interval = note_to_interval[note_track]
                            note_list = notes_per_interval[interval]
                            note_list[note_track] = (
                                note_start_times[note_track] - interval,
                                cumulative_time - interval,
                            )
                            notes_per_interval[interval] = note_list
                    del note_to_interval[note_track]
                    del note_start_times[note_track]

        # Add any remaining notes
        if notes_in_current_interval:
            notes_per_interval[current_interval_end - beat_ticks] = (
                notes_in_current_interval
            )

        return notes_per_interval

    def get_continuation_notes(
        self, notes_per_interval, ticks_per_beat, interval_in_beats
    ):
        ticks_per_interval = int(ticks_per_beat * interval_in_beats)
        continuation_note_dict = {}
        continuation_per_interval = {}
        for time, dicts in notes_per_interval.items():

            temp_continuation_note_dict = dict(continuation_note_dict)
            for note_channel in continuation_note_dict:
                if time >= continuation_note_dict[note_channel][1]:
                    del temp_continuation_note_dict[note_channel]
            continuation_note_dict = dict(temp_continuation_note_dict)
            for note_channel in temp_continuation_note_dict:
                temp_continuation_note_dict[note_channel] = (
                    temp_continuation_note_dict[note_channel][0] - time,
                    temp_continuation_note_dict[note_channel][1] - time,
                )
            continuation_per_interval[time] = temp_continuation_note_dict
            #         print(time, temp_continuation_note_dict)
            for note_channel in dicts.keys():
                exact_start_time = time + dicts[note_channel][0]
                exact_end_time = time + dicts[note_channel][1]
                continuation_note_dict[note_channel] = (
                    exact_start_time,
                    exact_end_time,
                )
        return continuation_per_interval

    def get_valid_notes_per_interval(
        self, notes_per_interval, continuation_notes_per_interval
    ):

        # HYPERPARAMETER
        start_threshold = 480
        end_threshold = 0

        valid_notes_per_interval = {}
        valid_start_notes_per_interval = {}
        valid_cont_notes_per_interval = {}
        for time, notes in notes_per_interval.items():
            valid_notes = []
            for note in notes:
                start, end = notes[note]
                if int(start) <= start_threshold:
                    valid_notes.append(note)
            valid_start_notes_per_interval[time] = valid_notes
            valid_notes_per_interval[time] = valid_start_notes_per_interval[time]

        for time, notes in continuation_notes_per_interval.items():
            valid_notes = []
            for note in notes:
                start, end = notes[note]
                if int(end) >= end_threshold:
                    valid_notes.append(note)
            valid_cont_notes_per_interval[time] = valid_notes
            valid_notes_per_interval[time].extend(valid_cont_notes_per_interval[time])

        return valid_notes_per_interval

    def get_key_nos(self, key):
        # print(os.getcwd())
        # with open(os.path.join(os.getcwd(), "note_name_no.json"), "r") as f:
        #     note_name_no = json.load(f)
        key_nos = []
        for note in self.note_name_no:
            if key == note[:-1]:
                key_nos.append(int(self.note_name_no[note]))
        return key_nos

    def get_chord_note_offset_short(self):
        # with open(os.path.join(os.getcwd(), "chord_note_offset_short.json"), "r") as f:
        #     chord_note_offset = json.load(f)
        for chord in self.chord_note_offset_short:
            note_offset = self.chord_note_offset_short[chord]
            updated_note_offset = [i - 12 if i >= 12 else i for i in note_offset]
            self.chord_note_offset_short[chord] = updated_note_offset
        return self.chord_note_offset_short

    def get_likely_chord(self, chord_note_offset, offset_notes):
        final_chord = ""
        for chord in chord_note_offset:
            for i in range(0, 12):
                note_offset = set(chord_note_offset[chord])
                new_note_offset = {
                    x + i - 12 if x + i >= 12 else x + i for x in note_offset
                }
                if new_note_offset == offset_notes:
                    final_chord = str(i) + "_" + chord
                    return final_chord
        for chord in chord_note_offset:
            for i in range(0, 12):
                note_offset = set(chord_note_offset[chord])
                new_note_offset = {
                    x + i - 12 if x + i >= 12 else x + i for x in note_offset
                }
                if new_note_offset <= offset_notes:
                    final_chord = str(i) + "_" + chord
                    return final_chord
        return "false"

    def get_offset_notes_per_interval(self, valid_notes_per_interval, key_nos):
        offset_notes_per_interval = {}
        for time in valid_notes_per_interval:
            notes = valid_notes_per_interval[time]
            offset_notes = []
            for note_layer in notes:
                note = int(note_layer.split("-")[0])
                offset = note - Commons.highest_smaller(key_nos, note)
                offset_notes.append(offset)
            offset_notes_per_interval[time] = set(offset_notes)
        return offset_notes_per_interval

    def get_offset_notes_merged_interval(self, offset_notes_per_interval, merge_range):
        offset_notes_merged_interval = {}
        range_ind = []
        merged_notes = set()
        for time in offset_notes_per_interval:
            offset_notes = offset_notes_per_interval[time]
            merged_notes = merged_notes.union(offset_notes)
            range_ind.append(time)

            if len(range_ind) == merge_range:
                offset_notes_merged_interval[tuple(range_ind.copy())] = (
                    merged_notes.copy()
                )
                merged_notes = set()
                range_ind = []
        return offset_notes_merged_interval

    def get_chord_per_interval(self, offset_notes_per_interval):
        chord_per_interval = {}
        chord_note_offset_short = self.get_chord_note_offset_short()
        for time in offset_notes_per_interval:
            offset_notes = offset_notes_per_interval[time]
            chord = self.get_likely_chord(chord_note_offset_short, set(offset_notes))
            chord_per_interval[time] = chord
        return chord_per_interval

    def populate_missing_chord_per_interval(
        self, chord_per_interval, chord_merged_2_interval, chord_merged_4_interval
    ):
        new_chord_per_interval = {}
        for key in chord_per_interval:
            chord = chord_per_interval[key]
            # pdb.set_trace()
            if chord == "false":
                for key_1 in chord_merged_2_interval:
                    if key in key_1:
                        chord = chord_merged_2_interval[key_1]
                        break
            # pdb.set_trace()
            if chord == "false":
                for key_1 in chord_merged_4_interval:
                    if key in key_1:
                        chord = chord_merged_4_interval[key_1]
                        break
            # pdb.set_trace()
            new_chord_per_interval[key] = chord
        return new_chord_per_interval

    def get_input_chord(self, input_rpp_file):
        mid = self.input_midi
        key = self.input_key

        key_nos = self.get_key_nos(key)

        messages = self.merge_tracks(mid, input_rpp_file)
        notes_per_interval = self.get_starting_notes_for_each_interval(
            messages, mid.ticks_per_beat, interval_in_beats=1
        )
        continuation_notes_per_interval = self.get_continuation_notes(
            notes_per_interval, mid.ticks_per_beat, interval_in_beats=1
        )

        valid_notes_per_interval = self.get_valid_notes_per_interval(
            notes_per_interval, continuation_notes_per_interval
        )
        offset_notes_per_interval = self.get_offset_notes_per_interval(
            valid_notes_per_interval, key_nos
        )
        # print(offset_notes_per_interval)
        offset_notes_merged_2_interval = self.get_offset_notes_merged_interval(
            offset_notes_per_interval, 2
        )
        offset_notes_merged_4_interval = self.get_offset_notes_merged_interval(
            offset_notes_per_interval, 4
        )
        chord_per_interval = self.get_chord_per_interval(offset_notes_per_interval)
        chord_merged_2_interval = self.get_chord_per_interval(
            offset_notes_merged_2_interval
        )
        chord_merged_4_interval = self.get_chord_per_interval(
            offset_notes_merged_4_interval
        )

        final_chord_per_interval = self.populate_missing_chord_per_interval(
            chord_per_interval, chord_merged_2_interval, chord_merged_4_interval
        )
        return (
            final_chord_per_interval,
            offset_notes_per_interval,
            valid_notes_per_interval,
        )

    def get_style_chord(self, style_rpp_file):
        mid = self.aligned_midi

        messages = self.merge_tracks(mid, style_rpp_file)
        notes_per_interval = self.get_starting_notes_for_each_interval(
            messages, mid.ticks_per_beat, interval_in_beats=1
        )
        continuation_notes_per_interval = self.get_continuation_notes(
            notes_per_interval, mid.ticks_per_beat, interval_in_beats=1
        )

        valid_notes_per_interval = self.get_valid_notes_per_interval(
            notes_per_interval, continuation_notes_per_interval
        )

        offset_notes_per_interval_final = {}
        final_chord_per_interval_final = {}
        for item in self.section_list:
            start_tick = TimeUtils.bar_beat_to_tick(item["input_start_beat"], self.time_sig, mid.ticks_per_beat)
            end_tick = TimeUtils.bar_beat_to_tick(item["input_end_beat"], self.time_sig, mid.ticks_per_beat)

            key = item["style_key"]
            key_nos = self.get_key_nos(key)

            offset_notes_per_interval = self.get_offset_notes_per_interval(
                valid_notes_per_interval, key_nos
            )
            temp = offset_notes_per_interval.copy()
            for interval in temp:
                if interval < start_tick or interval >= end_tick:
                    del offset_notes_per_interval[interval]
            # print(offset_notes_per_interval)
            offset_notes_merged_2_interval = self.get_offset_notes_merged_interval(
                offset_notes_per_interval, 2
            )
            offset_notes_merged_4_interval = self.get_offset_notes_merged_interval(
                offset_notes_per_interval, 4
            )
            chord_per_interval = self.get_chord_per_interval(offset_notes_per_interval)
            chord_merged_2_interval = self.get_chord_per_interval(
                offset_notes_merged_2_interval
            )
            chord_merged_4_interval = self.get_chord_per_interval(
                offset_notes_merged_4_interval
            )

            final_chord_per_interval = self.populate_missing_chord_per_interval(
                chord_per_interval, chord_merged_2_interval, chord_merged_4_interval
            )
            # print(final_chord_per_interval)

            offset_notes_per_interval_final.update(offset_notes_per_interval)
            final_chord_per_interval_final.update(final_chord_per_interval)

        return (
            final_chord_per_interval_final,
            offset_notes_per_interval_final,
            valid_notes_per_interval,
        )

    def router(self, input_rpp_file, output_rpp_file_path):
        print("Identifying chords in input and reference...")
        input_chord, input_offset, input_notes = self.get_input_chord(input_rpp_file)
        style_chord, style_offset, style_notes = self.get_style_chord(output_rpp_file_path)
        # print(input_offset)
        print("Chords Identified...")
        return (
            input_chord,
            input_offset,
            input_notes,
            style_chord,
            style_offset,
            style_notes,
        )
