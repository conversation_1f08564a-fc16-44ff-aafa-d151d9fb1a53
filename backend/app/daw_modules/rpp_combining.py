import shutil
import os
import re


class RppCombiner:
    def __init__(self, input_name, input_genre, input_tempo, section_list):
        # self.output_path = output_path # Not Used
        self.input_name = input_name
        self.input_genre = input_genre
        self.input_tempo = input_tempo
        self.section_list = section_list

    def find_chunks(self, lines, tag="<TRACK"):
        """
        Locate and extract all blocks starting with a tag (e.g., <TRACK), 
        correctly handling nested structures.
        """
        chunks = []
        inside_chunk = False
        stack = []
        current_chunk = []

        for line in lines:
            stripped = line.strip()

            # Detect opening tag
            if stripped.startswith(tag):
                if inside_chunk:
                    stack.append(tag)  # nested track start (rare but possible)
                else:
                    inside_chunk = True
                    current_chunk = []
                current_chunk.append(line)
                continue

            if inside_chunk:
                current_chunk.append(line)
                # Detect any nested opening tags inside track
                if stripped.startswith("<") and not stripped.startswith("</"):
                    stack.append(tag)
                # Detect closing of a block
                if stripped == ">":
                    if stack:
                        stack.pop()
                    else:
                        inside_chunk = False
                        chunks.append("".join(current_chunk))
                        current_chunk = []

        return chunks

    def filter_master_tracks(self,track_blocks, first_proj):
        """
        Filters out tracks that have names containing 'Master' if skip_master is True.
        """
        filtered_tracks = []
        for track in track_blocks:
            name_match = re.search(r'NAME\s+"([^"]+)"', track, re.IGNORECASE)
            if name_match:
                track_name = name_match.group(1)
                if (not first_proj) and "master" in track_name.lower():
                    # print(f"Skipping master track: {track_name}")
                    continue  # Skip this track
            filtered_tracks.append(track)
        return filtered_tracks

    def add_common_mix_bus(self, track_blocks, first_proj):
        new_track_blocks = track_blocks.copy()
        for i, track in enumerate(track_blocks):
            name_match = re.search(r'NAME\s+"([^"]+)"', track, re.IGNORECASE)
            if name_match:
                track_name = name_match.group(1)
                if first_proj and "mix" in track_name.lower():
                    new_name = re.sub(r'\bmix\b', 'COMMON MIX', track_name, flags=re.IGNORECASE)
                    new_track = track.replace(track_name, new_name)
                    new_track_blocks.insert(i, new_track)
        return new_track_blocks

    def adjust_bus_hierarchy(self, track_blocks, first_proj):
        """
        Set last ISBUS so that next rpp project adjust under proper hierarchy
        """
        abs_indent = 0
        for track in track_blocks[:-1]:
            name_match = re.search(r'ISBUS\s+(-?\d+)\s+(-?\d+)', track, re.IGNORECASE)
            if name_match:
                indent = name_match.group(2)
                abs_indent += int(indent)

        if first_proj:
            last_track_indent = 2 - abs_indent
        else:
            last_track_indent = -abs_indent
        last_track = track_blocks[-1]
        result = re.sub(r'(ISBUS\s+-?\d+)\s+-?\d+', lambda m: f"{m.group(1)} {last_track_indent}", last_track)
        track_blocks[-1] = result
        return track_blocks

    def find_track_end(self, track_blocks):
        final_end = 0
        for track in track_blocks:
            item_blocks = re.findall(r'<ITEM[\s\S]*?>', track)
            items_data = []
            
            for item in item_blocks:
                position_match = re.search(r'POSITION ([\d\.]+)', item)
                length_match = re.search(r'LENGTH ([\d\.]+)', item)
                if position_match and length_match:
                    position = float(position_match.group(1))
                    length = float(length_match.group(1))
                    end = position + length
                    
                    if end > final_end:
                        final_end = end
        return final_end

    def change_tempo_rpp(self, project_content, new_tempo):
        tempo_match = re.search(r'TEMPO ([\d\.]+)', project_content)
        if tempo_match:
            tempo = float(tempo_match.group(1))
            project_content = project_content.replace(f"TEMPO {str(tempo)}", f"TEMPO {str(new_tempo)}")
        return project_content

    def adjust_item_position(self, track_blocks, offset):
        new_track_blocks = []
        for track in track_blocks:
            item_blocks = re.findall(r'<ITEM[\s\S]*?>', track)
            items_data = []

            new_track = track
            for item in item_blocks:
                position_match = re.search(r'POSITION ([\d\.]+)', item)
                if position_match:
                    position = float(position_match.group(1))
                    new_position = position + offset

                    # An integer may be mentioned with or without decimal point as a string
                    if f"POSITION {str(position)}" in item:
                        new_item = item.replace(f"POSITION {str(position)}", f"POSITION {str(new_position)}")
                    else:
                        new_item = item.replace(f"POSITION {str(int(position))}", f"POSITION {str(new_position)}")
                    
                    new_track = new_track.replace(item, new_item)
            new_track_blocks.append(new_track)
        return new_track_blocks

    def extract_raw_tracks(self, filename):
        """
        Extracts track blocks from a Reaper project file.
        Returns (header, filtered_track_blocks, footer).
        """
        # Read and parse file
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Split into header, tracks, footer
        track_start_idx = None
        for idx, line in enumerate(lines):
            if line.strip().startswith('<TRACK'):
                track_start_idx = idx
                break

        if track_start_idx is None:
            raise ValueError("No tracks found in file: " + filename)

        header = "".join(lines[:track_start_idx])
        track_lines = lines[track_start_idx:]

        track_blocks = self.find_chunks(track_lines)

        total_track_lines = sum(len(t.splitlines()) for t in track_blocks)
        footer_lines = track_lines[total_track_lines:]
        footer = "".join(footer_lines)

        return header, track_blocks, footer

    def adjust_fx_route(self, track_blocks, prev_no_of_tracks, file_index):
        new_track_blocks = []
        if file_index == 0:
            prev_no_of_tracks += 1
        else:
            prev_no_of_tracks -= 1
        for track in track_blocks:
            
            pattern = r'(\s*AUXRECV\s+)(\d+)'

            # Function to replace the first integer with the new integer added to it
            def replace_first_integer(match):
                # Extract the first integer
                first_integer = int(match.group(2))
                # Add the new integer to the first extracted integer
                new_value = first_integer + prev_no_of_tracks
                # Return the modified part of the line with the updated integer
                return match.group(1) + str(new_value)
            
            # Replace all occurrences of "AUXRECV" with the updated integer in the text
            new_track = re.sub(pattern, replace_first_integer, track)

            
            new_track_blocks.append(new_track)
        return new_track_blocks

    def rename_tracks(self, track_blocks, project_id):
        new_track_blocks = []
        for track in track_blocks:
            name_match = re.search(r'NAME\s+"([^"]+)"', track, re.IGNORECASE)
            if name_match:
                track_name = name_match.group(1)
                if project_id:
                    new_track = track.replace(track_name, track_name + " _" + project_id)
                    new_track_blocks.append(new_track)
        return new_track_blocks

    def adjust_tracks(self, track_blocks, file_index, offset, prev_no_of_tracks, project_id):
        first_proj = file_index == 0

        adjusted_tracks = self.adjust_fx_route(track_blocks, prev_no_of_tracks, file_index)
        adjusted_tracks = self.filter_master_tracks(adjusted_tracks, first_proj)
        adjusted_tracks = self.add_common_mix_bus(adjusted_tracks, first_proj)
        adjusted_tracks = self.adjust_bus_hierarchy(adjusted_tracks, first_proj)
        adjusted_tracks = self.adjust_item_position(adjusted_tracks, offset)
        adjusted_tracks = self.rename_tracks(adjusted_tracks, project_id)

        track_end = self.find_track_end(adjusted_tracks)

        return adjusted_tracks, track_end

    def rename_media_files(self, track_blocks, file_rename_dict):
        new_track_blocks = []
        for track in track_blocks:
            for file in file_rename_dict:
                track = track.replace(file, file_rename_dict[file])
            new_track_blocks.append(track)
        return new_track_blocks

    def router(self, output_path:str):    # Extract tracks 1 and 2 from file1 and tracks 3 and 4 from file2
        tracks_file_list = []
        track_end = 0

        output_media_path = os.path.join(output_path, 'Media')

        if not os.path.exists(output_media_path):
            os.makedirs(output_media_path)
            
        media_file_lists = []
        for i, section in enumerate(self.section_list):
            if section['project_id'] == "" or section['section'] == "blank":
                continue
            if i > 0 and section['project_id'] == self.section_list[i-1]['project_id']:
                continue
            rpp_proj = section['style_rpp']
            media_folder_path = os.path.join(os.path.dirname(rpp_proj), "Media")
            file_rename_dict = {}
            if os.path.exists(media_folder_path):
                file_list = os.listdir(media_folder_path)
                for file in file_list:
                    if not os.path.isfile(os.path.join(media_folder_path, file)):
                        continue
                    bak_file = file
                    while file in media_file_lists:
                        file = "_" + file
                    file_rename_dict[bak_file] = file
                    media_file_lists.append(file)
                    source_file_path = os.path.join(media_folder_path, bak_file)
                    target_file_path = os.path.join(output_media_path, file)
                    shutil.copy(source_file_path, target_file_path)
                
            if i == 0:
                header, track_blocks, footer = self.extract_raw_tracks(rpp_proj)
            elif i == len(self.section_list) -1:
                _, track_blocks, footer = self.extract_raw_tracks(rpp_proj)
            else:
                _, track_blocks, footer = self.extract_raw_tracks(rpp_proj)

            track_blocks = self.rename_media_files(track_blocks, file_rename_dict)
            
            adjusted_tracks, track_end = self.adjust_tracks(track_blocks, i, track_end, len(tracks_file_list), section['project_id'])
            tracks_file_list += adjusted_tracks

        # Combine header and selected tracks
        new_project_content = header + ''.join(tracks_file_list) + footer
        
        new_project_content = self.change_tempo_rpp(new_project_content, self.input_tempo)

        # Write the new project file
        output_rpp_file_path = os.path.join(output_path, f"{self.input_name} _ output.rpp")
        with open(output_rpp_file_path, 'w') as f:
            f.write(new_project_content)

        return output_rpp_file_path