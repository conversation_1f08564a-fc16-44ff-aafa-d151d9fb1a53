import os
import mido
from mido import MidiFile
from app.utilities.midi_utils import MidiUtils
from app.utilities.time_utils import TimeUtils
from app.constants.constants import target_ticks_per_beat

class Aligner:
    def __init__(self, time_sig, input_midi, input_tempo, section_list):
        self.time_sig = time_sig
        self.input_midi = input_midi
        self.section_list = section_list
        self.input_tempo = input_tempo


    def get_tempo_from_midi(self, midi_file):
        for track in midi_file.tracks:
            for msg in track:
                if msg.type == "set_tempo":
                    # The tempo is represented in microseconds per quarter note
                    tempo_microseconds_per_qn = msg.tempo
                    # Convert microseconds per quarter note to beats per minute
                    tempo_bpm = 60 / (tempo_microseconds_per_qn / 1e6)
                    return tempo_bpm

        # If no tempo information is found, return a default value
        return None

    def update_tempo_in_midi(self, midi, new_tempo_bpm):
        new_midi = mido.MidiFile(ticks_per_beat=midi.ticks_per_beat)
        # Calculate the new tempo in microseconds per quarter note
        new_tempo_microseconds_per_qn = int(60 / new_tempo_bpm * 1e6)

        # Iterate through tracks and messages to update the tempo
        for track in midi.tracks:
            new_track = mido.MidiTrack()
            for msg in track:
                new_msg = msg.copy()
                if new_msg.type == "set_tempo":
                    # Update the tempo value
                    new_msg.tempo = new_tempo_microseconds_per_qn
                new_track.append(new_msg)
            new_midi.tracks.append(new_track)
        return new_midi

    def rename_tracks(self, midi, project_id):
        for track in midi.tracks:
            track.name = track.name + " _" + project_id
        return midi

    def route(self, temp_dir):
        print("Aligning references to input MIDI...")
        final_midi = mido.MidiFile(ticks_per_beat=target_ticks_per_beat)

        blank_offset = 0
        used_project_ids = []

        for i, item in enumerate(self.section_list):
            # Handling blank section
            if item["style_midi"] == "" or item["section"] == "blank":
                empty_midi = mido.MidiFile(ticks_per_beat=target_ticks_per_beat)

                input_start = item["input_start_beat"]
                input_start_tick = TimeUtils.bar_beat_to_tick(input_start, self.time_sig, self.input_midi.ticks_per_beat)

                input_end = item["input_end_beat"]
                input_end_tick = TimeUtils.bar_beat_to_tick(input_end, self.time_sig, self.input_midi.ticks_per_beat)

                input_len_ticks = input_end_tick - input_start_tick

                blank_offset += input_len_ticks

            else:
                new_midi = mido.MidiFile(ticks_per_beat=target_ticks_per_beat)
                style_midi = MidiFile(item["style_midi"])
                style_midi = MidiUtils.curate_midi(style_midi)
                style_midi_ticks_per_beat = style_midi.ticks_per_beat
                style_midi_len_ticks = MidiUtils.get_mid_len(style_midi)

                input_start = item["input_start_beat"]
                input_start_tick = TimeUtils.bar_beat_to_tick(input_start, self.time_sig, style_midi_ticks_per_beat)

                input_end = item["input_end_beat"]
                input_end_tick = TimeUtils.bar_beat_to_tick(input_end, self.time_sig, style_midi_ticks_per_beat)

                input_len_ticks = input_end_tick - input_start_tick

                no_of_rep = int(input_len_ticks / style_midi_len_ticks)
                remainder = input_len_ticks % style_midi_len_ticks
                remaining_frac = float(remainder / style_midi_len_ticks)

                if no_of_rep >= 1:
                    new_midi = MidiUtils.repeat_midi(style_midi, no_of_rep)
                    # if not os.path.exists(".\\test"):
                    #     os.makedirs(".\\test", True)
                    # new_midi.save(".\\test\\new.mid")
                    new_midi_path = os.path.join(temp_dir, "test_new.mid")
                    new_midi.save(new_midi_path)
                len_ = MidiUtils.get_mid_len(new_midi)

                if remaining_frac > 0:
                    remaining_style_midi_len_ticks = int(remaining_frac * style_midi_len_ticks)
                    cut_midi_from_first = MidiUtils.cut_midi(style_midi, 0, remaining_style_midi_len_ticks)
                    len_first = MidiUtils.get_mid_len(cut_midi_from_first)
                    cut_midi_from_last = MidiUtils.cut_midi(style_midi, style_midi_len_ticks - remaining_style_midi_len_ticks, style_midi_len_ticks)
                    len_last = MidiUtils.get_mid_len(cut_midi_from_last)
                    len_pewv_new_midi = MidiUtils.get_mid_len(new_midi)
                    if no_of_rep >= 1:
                        new_midi = MidiUtils.concatenate_midi(cut_midi_from_first, new_midi, True)
                    else:
                        new_midi = cut_midi_from_last
                    len_ = MidiUtils.get_mid_len(new_midi)

                new_midi = self.rename_tracks(new_midi, item["project_id"])
                
                len_prev_final_midi = MidiUtils.get_mid_len(final_midi)
                
                if item["project_id"] in used_project_ids:
                    final_midi = MidiUtils.concatenate_midi(final_midi, new_midi, True, blank_offset)
                else:
                    final_midi = MidiUtils.concatenate_midi(final_midi, new_midi, False, blank_offset)
                    used_project_ids.append(item["project_id"])
                len_next_final_midi = MidiUtils.get_mid_len(final_midi)
                blank_offset = 0
                
                

        final_midi = self.update_tempo_in_midi(final_midi, self.input_tempo)
        print("Alignment completed!!!")
        return final_midi
