from dotenv import load_dotenv
import os
load_dotenv()

class Environment:
    def __init__(self):
        self.NAME = os.getenv("NAME")
        self.MONGODB_URL = os.getenv("MONGODB_URL")
        self.AWS_REGION = os.getenv("AWS_REGION")
        self.AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY")
        self.AWS_SECRET_KEY = os.getenv("AWS_SECRET_KEY")
        self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


env = Environment()
