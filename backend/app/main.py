import os
import sys
import json
import platform
from typing import Any
import uvicorn
import requests
from bson.objectid import ObjectId
from fastapi import FastAPI, HTTPException, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from mido import MidiFile
from starlette.middleware.base import BaseHTTPMiddleware

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.websocket_router import router as websocket_router

from app.models.api_interface import (
    UploadStyleInterface,
    SearchStyleInterface,
    GetAnnotatorsInterface,
    GetGenresInterface,
    GetPitchesInterface,
    GetScalesInterface,
    GetTemposInterface,
    GetSourcesInterface,
    GetSectionsInterface,
    GetDurationsInterface,
    InputStyleInterface,
    GetMidiInfoInterface,
    TwelveScaleRenderInterface,
    ChatLoadFileInterface,
    ChatAskInterface,
)
from app.ai_assistant.chat_completion import chat_completion_function, chat_completion
from app.ai_assistant.assistant_state import AssistantState
from app.constants.constants import time_sign_map
from app.utilities.logger import logger
from app.utilities.time_utils import TimeUtils
from app.utilities.rpp_utils import RppUtils
from app.utilities.midi_utils import MidiUtils
from app.utilities.file_utils import FileUtils
from app.twelve_scale_render.core import router as twelve_scale_router
from app.utilities.commons import Commons
from app.daw_modules.style_alignment import Aligner
from app.utilities.s3 import AWS_S3, styles_bucket
from app.style_transfer.final_transfer import MIDI_Transfer
from app.utilities.db import connectMongo, mongoDB, OriginalStyleTable
from app.daw_modules.rpp_combining import RppCombiner
from app.daw_modules.chord_identification import ChordIdentifier
from app.ai_assistant.tool_registry import build_tool_map

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the WebSocket router
app.include_router(websocket_router)

connectMongo()


class LogRequestMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Log request method and path
        method = request.method
        path = request.url.path

        # Log query parameters
        query_params = dict(request.query_params)
        query_params_str = str(query_params)

        # Log request body
        request_body = await request.body()
        request_body_str = str(request_body.decode("utf-8"))

        # Construct the log message
        log_message = (
            f"{method}~{path} : [Body: {request_body_str} <> Query: {query_params_str}]"
        )
        logger.info(log_message)

        response = await call_next(request)
        return response


app.add_middleware(LogRequestMiddleware)

assistant_state = AssistantState.get()


def create_filter(q: Any):
    return q.model_dump(exclude_unset=True)


@app.get("/")
def root():
    return {
        "success": True,
        "message": "Welcome to Melodyze.ai Reaper Assistant",
        "version": "1.4.0",
    }


@app.get("/config_genres")
def config_genres():
    try:
        response = requests.get("https://ezydolem-api.melodyze.ai/utilities/v1/genres")
        genres = response.json().get("data", [])
        return {
            "success": True,
            "message": "success",
            "data": genres,
        }
    except Exception as e:
        logger.error(f"error: config_genres: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.get("/start_reaper")
def start_reaper():
    try:
        RppUtils.open_reaper()
        return {"success": True, "message": "REAPER has successfully opened."}
    except Exception as e:
        logger.error(f"error: start_reaper: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_styles")
def get_styles(
    data: SearchStyleInterface,
    page: int = Query(default=1, ge=1),
    page_size: int = Query(default=20, ge=1, le=50),
):
    """
    Accepts query parameters for pagination
    along with body data for filters.
    """
    try:
        filter_expression = create_filter(data)
        options: dict = {"skip": (page - 1) * page_size, "limit": page_size}

        total_count = mongoDB.processed_styles.coll.count_documents(filter_expression)
        styles = mongoDB.processed_styles.find(filter_expression, options=options)

        for style in styles:
            style.audio_path = AWS_S3.get_signed_url(style.audio_path)
            style.audio_with_vocals_path = AWS_S3.get_signed_url(
                style.audio_with_vocals_path
            )

        return {
            "success": True,
            "data": {
                "styles": styles,
                "paginated_cursor": {
                    "total": total_count,
                    "page": page,
                    "page_size": page_size,
                    "has_next": total_count > (page * page_size),
                    "has_previous": page > 1,
                },
            },
        }
    except Exception as e:
        print(f"error: get_styles: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_original_styles")
def get_original_styles(
    data: SearchStyleInterface,
    page: int = Query(default=1, ge=1),
    page_size: int = Query(default=20, ge=1, le=50),
):
    try:
        filter_expression = create_filter(data)

        style_ids = mongoDB.processed_styles.distinct("raw_style_id", filter_expression)
        styles = mongoDB.original_styles.find(
            {"_id": {"$in": [ObjectId(id) for id in style_ids]}}
        )
        total_count = len(styles)

        for style in styles:
            style.audio_path = AWS_S3.get_signed_url(style.audio_path)
            style.audio_with_vocals_path = AWS_S3.get_signed_url(
                style.audio_with_vocals_path
            )

        return {
            "success": True,
            "data": {
                "styles": styles,
                "paginated_cursor": {
                    "total": total_count,
                    "page": page,
                    "page_size": page_size,
                    "has_next": total_count > (page * page_size),
                    "has_previous": page > 1,
                },
            },
        }
    except Exception as e:
        print(f"error: get_original_styles: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_processed_style/{raw_style_id}")
def get_processed_style(raw_style_id: str):
    try:
        styles = mongoDB.processed_styles.find({"raw_style_id": ObjectId(raw_style_id)})
        for style in styles:
            style.audio_path = AWS_S3.get_signed_url(style.audio_path)
            style.audio_with_vocals_path = AWS_S3.get_signed_url(
                style.audio_with_vocals_path
            )

        return {
            "success": True,
            "data": {
                "styles": styles,
            },
        }
    except Exception as e:
        print(f"error: get_processed_style: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_annotators")
def get_annotators(query: GetAnnotatorsInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("annotator", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_annotators: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_genres")
def get_genres(query: GetGenresInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("genre", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_genres: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_pitches")
def get_pitches(query: GetPitchesInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("pitch", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_pitches: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_scales")
def get_scales(query: GetScalesInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("scale", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_scales: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_tempos")
def get_tempos(query: GetTemposInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("tempo", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_tempos: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_sources")
def get_sources(query: GetSourcesInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("source", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_sources: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_sections")
def get_sections(query: GetSectionsInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("section", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_sections: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_durations")
def get_durations(query: GetDurationsInterface):
    try:
        filter_expression = create_filter(query)
        items = mongoDB.processed_styles.distinct("duration_in_bars", filter_expression)
        return {"data": items}
    except Exception as e:
        print(f"error: get_durations: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/upload_style")
def upload_style(data: UploadStyleInterface):
    # TODO: pending field {created_by: ObjectId()}  //user_id
    session_id = TimeUtils.datetime_str()
    temp_dir = FileUtils.temp_dir(f"upload_style_{session_id}")
    output_dir = data.output_directory
    output_dir = (
        os.path.join(output_dir, f"{data.source}_{session_id}")
        if data.source
        else os.path.join(output_dir, f"unknown_source_{session_id}")
    )
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"output_dir: {output_dir}")
    try:
        data_dict = data.model_dump()
        del data_dict["output_directory"]
        data_dict["source"] = data.source.lower() if data.source else ""
        proj_ref_id = RppUtils.get_project_ref_id(data_dict["project_file_name"])
        raw_style_id = ObjectId()
        logger.info(f"raw_style_id: {str(raw_style_id)}")

        midi_file_zip_path: str = data_dict["midi_zip_path"]
        midi_extract_path = os.path.join(temp_dir, "midi")
        midi_file_path = FileUtils.unzip_file(
            midi_file_zip_path, midi_extract_path, "mid"
        )

        audio_file_path = data_dict["audio_path"]
        audio_vocal_file_path = data_dict["audio_with_vocals_path"]

        project_zip_path = data_dict["project_zip_path"]
        project_extract_path = os.path.join(temp_dir, "project")
        project_file_path = FileUtils.unzip_file(
            project_zip_path, project_extract_path, "rpp"
        )

        markers = MidiUtils.trim_midi_by_markers(
            midi_file_path, output_dir, data.time_signature, data.tempo
        )
        logger.info("MIDI trimmed\n")

        markers = RppUtils.trim_audio_by_markers(
            audio_file_path, audio_vocal_file_path, output_dir, markers
        )
        logger.info("Audio trimmed\n")

        markers = RppUtils.trim_rpp_by_markers(project_file_path, output_dir, markers)
        logger.info("RPP trimmed\n")

        styles_to_insert = []
        for marker in markers:
            logger.info(f"Processing marker: {marker['name']}")
            style = data_dict.copy()
            style_id = ObjectId()
            style["_id"] = style_id
            style["section"] = MidiUtils.get_section_mapping(marker["name"])
            style["raw_style_id"] = raw_style_id
            style["duration_in_bars"] = marker["end_bar"] - marker["start_bar"]
            style["created_at"] = TimeUtils.timestamp_mills()
            style["created_by"] = data_dict["annotator"]
            style["project_ref_id"] = proj_ref_id

            # zip trimmed midi and upload to s3
            trimmed_midi_zip_path = marker["cut_midi_filepath"].replace(".mid", ".zip")
            FileUtils.zip_file(marker["cut_midi_filepath"], trimmed_midi_zip_path)

            # zip trimmed rpp and upload to s3
            trimmed_rpp_zip_path = marker["cut_rpp_filepath"] + ".zip"
            FileUtils.zip_file(marker["cut_rpp_filepath"], trimmed_rpp_zip_path)

            processed_style_s3_key = (
                f"processed/{raw_style_id}/{style['section']}_{style_id}"
            )
            upload_tasks = [
                (
                    trimmed_midi_zip_path,
                    f"{processed_style_s3_key}/midi.zip",
                    "midi_zip_path",
                ),
                (
                    trimmed_rpp_zip_path,
                    f"{processed_style_s3_key}/project.zip",
                    "project_zip_path",
                ),
                (
                    marker["cut_audio_filepath"],
                    f"{processed_style_s3_key}/audio.mp3",
                    "audio_path",
                ),
                (
                    marker["cut_audio_with_vocals_filepath"],
                    f"{processed_style_s3_key}/audio_with_vocals.mp3",
                    "audio_with_vocals_path",
                ),
            ]

            for local_path, s3_key, dict_key in upload_tasks:
                AWS_S3.upload_file(local_path, styles_bucket, s3_key)
                style[dict_key] = f"/{styles_bucket}/{s3_key}"

            styles_to_insert.append(style)
            logger.info(f"Marker {marker['name']} processed\n")

        original_style_s3_key = f"original/{raw_style_id}"
        upload_tasks = [
            (midi_file_zip_path, f"{original_style_s3_key}/midi.zip", "midi_zip_path"),
            (
                project_file_path,
                f"{original_style_s3_key}/project.zip",
                "project_zip_path",
            ),
            (audio_file_path, f"{original_style_s3_key}/audio.mp3", "audio_path"),
            (
                audio_vocal_file_path,
                f"{original_style_s3_key}/audio_with_vocals.mp3",
                "audio_with_vocals_path",
            ),
        ]

        for local_path, s3_key, dict_key in upload_tasks:
            AWS_S3.upload_file(local_path, styles_bucket, s3_key)
            data_dict[dict_key] = f"/{styles_bucket}/{s3_key}"

        logger.info("Uploaded Original files to S3")

        data_dict["_id"] = raw_style_id
        data_dict["created_at"] = TimeUtils.timestamp_mills()
        # TODO: need to get user_id from token
        data_dict["created_by"] = data_dict["annotator"]
        data_dict["project_ref_id"] = proj_ref_id
        mongoDB.original_styles.create(data_dict)
        logger.info("Original Styles inserted into DB\n")

        inserted_data = []
        if len(styles_to_insert) > 0:
            inserted_data = mongoDB.processed_styles.insert_many(data=styles_to_insert)

        logger.info("Processed Styles inserted into DB\n")

        FileUtils.delete_dir(temp_dir)
        logger.info(
            f"Please verify the MIDI, Audio and RPP file cuts in the output directory: {output_dir}"
        )

        return {
            "success": True,
            "data": {"styles": inserted_data, "markers": markers},
        }
    except Exception as e:
        logger.info(f"error: upload_style: {str(e)}")
        FileUtils.delete_dir(temp_dir)
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/get_midi_info")
def get_midi_info(data: GetMidiInfoInterface):
    session_id = TimeUtils.datetime_str()
    temp_dir = FileUtils.temp_dir(f"midi_info_{session_id}")
    try:
        midi_file_path = FileUtils.unzip_file(data.midi_path, temp_dir, "mid")
        midi_secions = MidiUtils.calculate_markers_with_bars_and_beats(
            midi_file_path, data.time_signature, data.tempo
        )["markers"]
        FileUtils.delete_dir(temp_dir)
        return {
            "success": True,
            "data": {
                "midi_path": data.midi_path,
                "sections": midi_secions,
            },
        }
    except Exception as e:
        print(f"error: get_midi_info: {str(e)}")
        FileUtils.delete_dir(temp_dir)
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/style_transfer")
def style_transfer(data: InputStyleInterface):
    session_id = TimeUtils.datetime_str()
    temp_dir = FileUtils.temp_dir(f"style_transfer_{session_id}")

    final_output_dir = os.path.join(
        data.output_directory, f"{data.song_name} _ {data.genre}_{session_id}"
    )
    if platform.system() == "Windows":
        final_output_dir = r"\\?\\" + final_output_dir
    os.makedirs(final_output_dir, exist_ok=True)
    try:
        RppUtils.open_reaper()

        input_midi_zip_path = data.input_midi_zip_path
        input_midi_extract_path = os.path.join(temp_dir, "input_midi")
        input_midi_file_path = FileUtils.unzip_file(
            input_midi_zip_path, input_midi_extract_path, "mid"
        )

        input_tempo = int(data.tempo)
        input_key = data.pitch.replace("_sharp", "#")
        # input_scale = data.scale
        input_time_sig = time_sign_map[data.time_signature]
        sections = []
        data.sections.sort(key=lambda x: x.sl_no)

        for sec_ind, section in enumerate(data.sections):
            logger.info(f"Processing section: {sec_ind + 1}")
            if section.style_id and section.is_blank is not True:
                # TODO: Update saved_style Type
                saved_style: Any = mongoDB.processed_styles.find_by_id(section.style_id)
                logger.info(f"saved_style: {saved_style}")

                file_tasks = [
                    # (zip_key_path, local_zip_name, extract_dir_name, unzip_ext, output_var_name)
                    (
                        "project_zip_path",
                        "project_zip",
                        "project",
                        "rpp",
                        "project_file_path",
                    ),
                    ("midi_zip_path", "midi_zip", "midi", "mid", "midi_file_path"),
                ]

                output_paths = {}

                for (
                    zip_key_path,
                    local_zip_name,
                    extract_dir_name,
                    unzip_ext,
                    output_var_name,
                ) in file_tasks:
                    zip_key = saved_style.get(zip_key_path).replace(
                        f"/{styles_bucket}/", ""
                    )
                    local_zip_path = os.path.join(
                        temp_dir, f"{local_zip_name}_{saved_style.get('_id')}.zip"
                    )
                    AWS_S3.download_file(styles_bucket, zip_key, local_zip_path)

                    extract_path = os.path.join(
                        temp_dir, f"{extract_dir_name}_{saved_style.get('_id')}"
                    )
                    file_path = FileUtils.unzip_file(
                        local_zip_path, extract_path, unzip_ext
                    )
                    output_paths[output_var_name] = file_path

                sections.append(
                    {
                        "section": saved_style.get("marker"),
                        "input_start_beat": f"{section.start_bar}.{section.start_beat}",
                        "input_end_beat": f"{section.end_bar}.{section.end_beat}",
                        "style_midi": output_paths["midi_file_path"],
                        "style_rpp": output_paths["project_file_path"],
                        "style_key": saved_style.get("pitch").replace("_sharp", "#"),
                        "style_scale": saved_style.get("scale"),
                        "project_id": saved_style.get("project_ref_id"),
                    }
                )
            else:
                sections.append(
                    {
                        "section": "blank",
                        "input_start_beat": f"{section.start_bar}.{section.start_beat}",
                        "input_end_beat": f"{section.end_bar}.{section.end_beat}",
                        "style_midi": "",
                        "style_key": "",
                        "style_scale": "",
                        "project_id": "",
                    }
                )
            logger.info(f"Section {section.style_id} processed")

        logger.info(f"sections completed: {sections}")

        input_midi = MidiFile(input_midi_file_path)
        curated_midi = MidiUtils.curate_midi(input_midi)

        rpp_combiner = RppCombiner(data.song_name, data.genre, data.tempo, sections)
        output_rpp_file_path = rpp_combiner.router(final_output_dir)
        logger.info("RPP files combined")

        # MIDI Aligner
        aligner = Aligner(input_time_sig, curated_midi, input_tempo, sections)
        aligned_midi = aligner.route(temp_dir)
        logger.info("Style MIDI aligned to input midi")

        # Chord Indentification
        logger.info("Identifying chords from style and input")
        chord_identifier = ChordIdentifier(
            input_time_sig, curated_midi, input_key, aligned_midi, sections
        )

        (
            input_chord,
            input_offset,
            input_notes,
            style_chord,
            style_offset,
            style_notes,
        ) = chord_identifier.router("", "")  # TODO: New parameters to be added
        logger.info("Chords identified")

        # Final Transfer
        logger.info("Transferring style")
        midi_transfer = MIDI_Transfer(
            input_time_sig,
            input_chord,
            input_offset,
            input_notes,
            style_chord,
            style_offset,
            style_notes,
            input_key,
            aligned_midi,
            sections,
        )
        transferred_midi = midi_transfer.route()
        logger.info("Style transferred")

        output_midi_path = os.path.join(
            final_output_dir, f"{data.song_name} _ {data.genre}.mid"
        )
        transferred_midi.save(output_midi_path)
        logger.info("Output midi saved")

        MidiUtils.import_midi_into_reaper(
            output_rpp_file_path, output_midi_path, temp_dir
        )
        logger.info("Output midi imported into reaper")

        # output midi save to request dump table / in  folder
        output_rpp_zip_path = output_rpp_file_path.replace(".rpp", ".zip")
        FileUtils.zip_file(output_rpp_file_path, output_rpp_zip_path)
        output_midi_zip_path = output_midi_path.replace(".mid", ".zip")
        FileUtils.zip_file(output_midi_path, output_midi_zip_path)

        input_midi_s3_key = f"style_transfer/input_midi/{session_id}.zip"
        output_midi_s3_key = f"style_transfer/output_midi/{session_id}.zip"
        output_project_s3_key = f"style_transfer/output_project/{session_id}.zip"
        AWS_S3.upload_file(input_midi_zip_path, styles_bucket, input_midi_s3_key)
        AWS_S3.upload_file(output_midi_zip_path, styles_bucket, output_midi_s3_key)
        AWS_S3.upload_file(output_rpp_zip_path, styles_bucket, output_project_s3_key)
        logger.info("Uploaded to s3")

        transfer_item_to_save = data.model_dump()
        transfer_item_to_save["_id"] = ObjectId()
        transfer_item_to_save["created_at"] = TimeUtils.timestamp_mills()
        transfer_item_to_save["input_midi_zip_path"] = (
            f"/{styles_bucket}/{input_midi_s3_key}"
        )
        transfer_item_to_save["output_midi_zip_path"] = (
            f"/{styles_bucket}/{output_midi_s3_key}"
        )
        transfer_item_to_save["output_rpp_zip_path"] = (
            f"/{styles_bucket}/{output_project_s3_key}"
        )

        mongoDB.transferred_styles.create(transfer_item_to_save)
        logger.info("Output uploaded to database")

        FileUtils.delete_dir(temp_dir)
        return {"success": True, "data": {"final_output_dir": final_output_dir}}

    except Exception as e:
        logger.info(f"error: style_transfer: {str(e)}")
        FileUtils.delete_dir(temp_dir)
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.get("/shutdown")
def shutdown():
    raise SystemExit("Shutting down FastAPI server")


@app.delete("/delete_style/{raw_style_id}")
def delete_style(raw_style_id: str):
    """
    Delete a style and all its associated files.
    1. Get the style from styles_master
    2. Find the original entry using raw_style_id
    3. Delete from original_styles
    4. Delete all related entries from styles_master
    5. Delete all associated files from S3
    """
    try:
        original_style: OriginalStyleTable | None = mongoDB.original_styles.find_by_id(
            raw_style_id
        )
        if not original_style:
            raise HTTPException(status_code=404, detail="Original Style not found.")

        # Find all styles with this raw_style_id
        related_styles = mongoDB.processed_styles.find(
            {"raw_style_id": ObjectId(raw_style_id)}
        )

        # Collect all file paths to delete
        files_to_delete = []

        files_to_delete.extend(
            [
                original_style.audio_path.replace(f"/{styles_bucket}/", ""),
                original_style.audio_with_vocals_path.replace(f"/{styles_bucket}/", ""),
                original_style.midi_zip_path.replace(f"/{styles_bucket}/", ""),
                original_style.project_zip_path.replace(f"/{styles_bucket}/", ""),
            ]
        )

        # Add files from related styles
        for related_style in related_styles:
            files_to_delete.extend(
                [
                    related_style.audio_path.replace(f"/{styles_bucket}/", ""),
                    related_style.audio_with_vocals_path.replace(
                        f"/{styles_bucket}/", ""
                    ),
                    related_style.midi_zip_path.replace(f"/{styles_bucket}/", ""),
                    related_style.project_zip_path.replace(f"/{styles_bucket}/", ""),
                ]
            )

        # Delete files from S3
        for file_path in files_to_delete:
            if file_path:  # Skip empty paths
                try:
                    AWS_S3.delete_file(styles_bucket, file_path)
                except Exception as e:
                    print(f"Warning: Could not delete file {file_path}: {str(e)}")

        # Delete from original_styles
        mongoDB.original_styles.delete_one({"_id": ObjectId(raw_style_id)})
        mongoDB.processed_styles.delete_many({"raw_style_id": ObjectId(raw_style_id)})

        return {
            "status": "success",
            "message": "Style and all associated files deleted successfully",
        }
    except Exception as e:
        print(f"error: delete_style: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/twelve_scale_render")
def twelve_scale_render(data: TwelveScaleRenderInterface):
    session_id = TimeUtils.datetime_str()
    proj_filename, _ = FileUtils.get_filename(data.project_zip_path)
    normalized_fn = Commons.normalize_string(proj_filename)
    temp_dir = os.path.join(data.output_dir, f"ts_render_{normalized_fn}_{session_id}")
    try:
        midi_extract_path = os.path.join(temp_dir, "midi")
        midi_file_path = FileUtils.unzip_file(
            data.midi_zip_path, midi_extract_path, "mid"
        )

        project_extract_path = os.path.join(temp_dir, "project")
        project_file_path = FileUtils.unzip_file(
            data.project_zip_path, project_extract_path, "rpp"
        )

        router_config = {
            "MIDI_path": midi_file_path,
            "project_path": project_file_path,
            "output_dir": data.output_dir,
            "default_scale": data.default_scale,
            "up_shift": data.up_shift,
            "down_shift": data.down_shift,
            "save_12_session": data.save_session,
            "total_wait_time": data.wait_time,
        }

        twelve_scale_router(router_config)
        FileUtils.delete_dir(temp_dir)

        return {
            "success": True,
            "message": "Twelve scale rendering started successfully.",
            "data": {"router_config": router_config},
        }

    except Exception as e:
        logger.error(f"error: twelve_scale_render: {str(e)}")
        FileUtils.delete_dir(temp_dir)
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/chat/file_uploaded")
def chat_file_uploaded(data: ChatLoadFileInterface):
    try:
        assistant_state.load_project(data.project_filepath)
        return {
            "success": True,
            "data": {"conversation": assistant_state.conversation_history},
        }
    except Exception as e:
        logger.error(f"error: chat_file_uploaded: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


@app.post("/chat/ask")
def chat_ask(data: ChatAskInterface):
    try:
        # ----- 1  append user message -----
        assistant_state.conversation_history.append(
            {"role": "user", "content": data.user_input}
        )

        # ----- 2  build outbound list with fresh project summary -----
        outbound = assistant_state.conversation_history[:]
        outbound.insert(
            1,
            {
                "role": "system",
                "name": "project_summary",
                "content": assistant_state.project_summary,
            },
        )

        # ----- 3  first OpenAI call -----
        assistant = chat_completion_function(outbound)
        assistant_state.conversation_history.append(assistant.to_dict())

        # ----- 4  if tool requested ----
        if assistant.tool_calls:
            tool_map = build_tool_map()
            for tool_call in assistant.tool_calls:
                fn_name = tool_call.function.name
                args = json.loads(tool_call.function.arguments or "{}")
                print("Tool Call: ", tool_call)

                tool_fn = tool_map.get(fn_name)
                if tool_fn is None:
                    result = f"Error: Unknown tool '{fn_name}'"
                else:
                    try:
                        result = tool_fn(**args)
                    except Exception as e:
                        result = f"Error while executing '{fn_name}': {e}"

                # function role message
                assistant_state.conversation_history.append(
                    {
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": fn_name,
                        "content": result,
                    }
                )

            # 5. Follow-up LLM call so it can craft the final user-visible reply
            outbound2 = assistant_state.conversation_history[:]
            # outbound2.insert(
            #     1,
            #     {"role": "system", "name": "project_summary",
            #      "content": assistant_state.project_summary},
            # )
            follow_up = chat_completion(outbound2)
            assistant_state.conversation_history.append(follow_up.to_dict())

        # ----- 6  return all -----
        return {
            "success": True,
            "data": {"conversation": assistant_state.conversation_history},
        }

    except Exception as e:
        logger.error(f"error: chat_ask: {str(e)}")
        assistant_state.conversation_history.append(
            {
                "role": "assistant",
                "content": f"I'm sorry, I couldn't process your request. Please try again \n Error: {str(e)}",
            }
        )
        return {
            "success": True,
            "data": {"conversation": assistant_state.conversation_history},
        }


@app.post("/chat/reset")
def chat_reset():
    try:
        assistant_state.discard()
        return {
            "success": True,
            "data": {"conversation": assistant_state.conversation_history},
        }
    except Exception as e:
        logger.error(f"error: chat_reset: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {str(e)}"
        ) from e


if __name__ == "__main__":
    # Run with WebSocket support
    uvicorn.run(app, host="127.0.0.1", port=8009, ws_max_size=16777216)
