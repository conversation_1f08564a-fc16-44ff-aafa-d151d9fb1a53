from typing import Optional
from pydantic import BaseModel

class UploadStyleInterface(BaseModel):
    time_signature: str
    swing: bool
    genre: str
    pitch: str
    scale: str
    tempo: str
    source: Optional[str] = None
    audio_path: str
    audio_with_vocals_path: str
    midi_zip_path: str
    project_zip_path: str
    project_file_name: str
    annotator: str
    output_directory: str

class SearchStyleInterface(BaseModel):
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    time_signature: Optional[str] = None
    swing: Optional[bool] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None
    duration_in_bars: Optional[int] = None  

class GetAnnotatorsInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None      

class GetGenresInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None

class GetPitchesInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None

class GetScalesInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None

class GetTemposInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None

class GetSourcesInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    section: Optional[str] = None  
    duration_in_bars: Optional[int] = None

class GetSectionsInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    duration_in_bars: Optional[int] = None

class GetDurationsInterface(BaseModel):
    time_signature: str
    swing: bool
    project_ref_id: Optional[str] = None
    annotator: Optional[str] = None
    genre: Optional[str] = None
    pitch: Optional[str] = None
    scale: Optional[str] = None
    tempo: Optional[str] = None
    source: Optional[str] = None
    section: Optional[str] = None 
    
class GetPutSignedUrlsInterface(BaseModel):
    content_type: str

class StyleSection(BaseModel):
    style_id: Optional[str] = ""
    is_blank: bool
    sl_no: int
    start_bar: int
    start_beat: int
    end_bar: int
    end_beat: int
    input_midi_section: str
    genre: Optional[str] = None
    source: Optional[str] = None


class InputStyleInterface(BaseModel):
    time_signature: str
    swing: bool
    genre: str
    pitch: str
    scale: str
    tempo: str
    input_midi_zip_path: str
    song_name: str
    project_ref_id: Optional[str] = None
    output_directory: str
    sections: list[StyleSection]


class GetMidiInfoInterface(BaseModel):
    midi_path: str
    time_signature: str
    tempo: int

class TwelveScaleRenderInterface(BaseModel):
    default_scale: str
    down_shift: int
    up_shift: int
    project_zip_path: str
    midi_zip_path: str
    output_dir: str
    save_session: bool
    wait_time: int

class ChatLoadFileInterface(BaseModel):
    project_filepath: str
    midi_filepath: str
    output_dir: str

class ChatAskInterface(BaseModel):
    user_input: str
