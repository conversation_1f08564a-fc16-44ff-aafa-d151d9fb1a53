import mido
from mido import Message
from app.constants.gsheets import Gsheet
from app.utilities.midi_utils import MidiUtils
from app.utilities.time_utils import TimeUtils
from app.utilities.commons import Commons
ticks_per_beat = None

class MIDI_Transfer:
    def __init__(self, time_sig, input_chord_per_interval, input_offset_notes_per_interval, input_valid_notes_per_interval,
                 style_chord_per_interval, style_offset_notes_per_interval, style_valid_notes_per_interval,
                 input_key, aligned_style_midi, section_list):
        self.time_sig = time_sig
        self.input_chord_per_interval = input_chord_per_interval
        self.input_offset_notes_per_interval = input_offset_notes_per_interval
        # print(self.input_offset_notes_per_interval)
        self.input_valid_notes_per_interval = input_valid_notes_per_interval
        self.style_chord_per_interval = style_chord_per_interval
        self.style_offset_notes_per_interval = style_offset_notes_per_interval
        self.style_valid_notes_per_interval = style_valid_notes_per_interval

        self.input_key = input_key
        self.aligned_style_midi= aligned_style_midi
        global ticks_per_beat
        ticks_per_beat = self.aligned_style_midi.ticks_per_beat
        self.section_list = section_list
        # self.chord_to_root = self.get_chord_to_root()
        self.chord_to_root = Gsheet.get_chord_root()
        self.note_name_no = Gsheet.get_note_name_no()


    # def get_chord_to_root(self):
    #     # with open(os.path.join(os.getcwd(), "chord_to_root.json"), "r") as f:
    #     #     chord_to_root = json.load(f)
    #     return chord_to_root

    def is_in_hierarchy(self, chord_1, chord_2):
        if chord_1 == chord_2:
            return True
        # pdb.set_trace()
        chord_note_1 = chord_1.split("_", 1)[0]
        chord_name_1 = chord_1.split("_", 1)[1]
        chord_note_2 = chord_2.split("_", 1)[0]
        chord_name_2 = chord_2.split("_", 1)[1]

        if not chord_note_1 == chord_note_2:
            return False

        while True:
            if chord_name_1 not in self.chord_to_root:
                break
            if self.chord_to_root[chord_name_1] == chord_name_2:
                return True
            chord_name_1 = self.chord_to_root[chord_name_1]

        while True:
            if chord_name_2 not in self.chord_to_root:
                break
            if self.chord_to_root[chord_name_2] == chord_name_1:
                return True
            chord_name_2 = self.chord_to_root[chord_name_2]

        return False

    def is_chord_change_next(self, current_chord, next_chord):
        if next_chord == 'false':
            return False
        if self.is_in_hierarchy(current_chord, next_chord) == True:
            return False
        else:
            return True

    def populate_missing_chords(self, chord_per_interval):
        # print(chord_per_interval)
        populated_chord_per_interval = dict()
        last_valid_chord = ""
        for index in chord_per_interval:
            chord = chord_per_interval[index]
            if not chord == 'false':
                last_valid_chord = chord
                break
        # last_valid_chord = chord_per_interval['0']
        populated_chord_per_interval[0] = last_valid_chord
        for tick_key in chord_per_interval:
            next_ind = (tick_key + ticks_per_beat)
            if not next_ind in chord_per_interval:
                continue
            if chord_per_interval[next_ind] == 'false':
                populated_chord_per_interval[next_ind] = last_valid_chord
            else:
                last_valid_chord = chord_per_interval[next_ind]
                populated_chord_per_interval[next_ind] = last_valid_chord
        # print(populated_chord_per_interval)
        return populated_chord_per_interval

    def reduce_one_note(self, note_list, chord):
        # with open("chord_note_offset_short.json", "r") as f:
            # chord_note_offset = json.load(f)
        chord_note_offset = Gsheet.get_chord_note_offset_short()
        chord_note = int(chord.split("_", 1)[0])
        chord_name = chord.split("_", 1)[1]

        chord_note_list = [x + chord_note - 12 if x + chord_note >= 12 else x + chord_note for x in
                           chord_note_offset[chord_name]]

        if len(note_list) > len(chord_note_list):
            difference = list(set(note_list) - set(chord_note_list))
            note_list_set = set(note_list)
            note_list_set.remove(difference[0])
            return list(note_list_set)
        else:
            while True:
                if chord_name not in self.chord_to_root:
                    return note_list
                chord_name = self.chord_to_root[chord_name]
                chord_note_list = [x + chord_note - 12 if x + chord_note >= 12 else x + chord_note for x in
                                   chord_note_offset[chord_name]]
                if len(note_list) > len(chord_note_list):
                    difference = list(set(note_list) - set(chord_note_list))
                    note_list_set = set(note_list)
                    note_list_set.remove(difference[0])
                    return list(note_list_set)

    def get_next_chord_change_marker(self, chord_per_interval):
        next_chord_change_marker = dict()
        for tick_key in chord_per_interval:
            if (tick_key + ticks_per_beat) in chord_per_interval.keys():
                chord_change = self.is_chord_change_next(chord_per_interval[tick_key],
                                                    chord_per_interval[(tick_key + ticks_per_beat)])
                next_chord_change_marker[tick_key] = chord_change
            else:
                next_chord_change_marker[tick_key] = False
        return next_chord_change_marker

    def get_key_nos(self, key):
        # with open(os.path.join(os.getcwd(), "note_name_no.json"), "r") as f:
        #     note_name_no = json.load(f)
        key_nos = []
        for note in self.note_name_no:
            if key == note[:-1]:
                key_nos.append(int(self.note_name_no[note]))
        return key_nos

    def get_chord_note_offset(self):
        # with open("chord_note_offset_short.json", "r") as f:
        #     chord_note_offset = json.load(f)
        chord_note_offset = Gsheet.get_chord_note_offset_short()
        for chord in chord_note_offset:
            note_offset = chord_note_offset[chord]
            updated_note_offset = [i - 12 if i >= 12 else i for i in note_offset]
            chord_note_offset[chord] = updated_note_offset
        return chord_note_offset

    def get_likely_chord(self, chord_note_offset, offset_notes):
        final_chord = ''
        for chord in chord_note_offset:
            for i in range(0, 12):
                note_offset = set(chord_note_offset[chord])
                new_note_offset = {x + i - 12 if x + i >= 12 else x + i for x in note_offset}
                if new_note_offset == offset_notes:
                    final_chord = str(i) + "_" + chord
                    return final_chord
        for chord in chord_note_offset:
            for i in range(0, 12):
                note_offset = set(chord_note_offset[chord])
                new_note_offset = {x + i - 12 if x + i >= 12 else x + i for x in note_offset}
                if new_note_offset <= offset_notes:
                    final_chord = str(i) + "_" + chord
                    return final_chord
        return 'false'

    # This function will populate each of the beats (tick keys) with the notes that are played in the same chord region
    def expand_notes_per_interval(self, input_next_chord_change_marker):
        input_expand_notes_per_interval = dict()
        tick_set = set()
        note_list = list()
        for tick_key in input_next_chord_change_marker:
            tick_set.add(tick_key)
            # print(self.input_offset_notes_per_interval)
            for note in self.input_offset_notes_per_interval[int(tick_key)]:
                if note not in note_list:
                    note_list.append(note)
            # print(tick_key)
            # print(str(input_next_chord_change_marker[tick_key]))
            # print("Tick_Set:" + str(tick_set))
            # print("Note_list:" + str(note_list))
            # print("This_Notes:" + str(self.input_offset_notes_per_interval[int(tick_key)]))
            if input_next_chord_change_marker[tick_key] == True:
                for tick in tick_set:
                    input_expand_notes_per_interval[tick] = note_list.copy()
                tick_set = set()
                note_list = list()
        for tick in tick_set:
            input_expand_notes_per_interval[tick] = note_list.copy()
        return input_expand_notes_per_interval

    def get_transfer_notes_per_interval(self, input_expand_notes_per_interval, input_updated_chord_per_interval):
        input_transfer_notes_per_interval = dict()
        prev_tick_key = None
        # print(input_expand_notes_per_interval)
        for tick_key in self.style_chord_per_interval:

            if tick_key not in input_expand_notes_per_interval:
                input_expand_notes_per_interval[int(tick_key)] = input_expand_notes_per_interval[int(prev_tick_key)]

            if tick_key not in input_updated_chord_per_interval:
                input_updated_chord_per_interval[tick_key] = input_updated_chord_per_interval[prev_tick_key]

            if len(input_expand_notes_per_interval[tick_key]) <= len(self.style_offset_notes_per_interval[tick_key]):
                input_transfer_notes_per_interval[tick_key] = input_expand_notes_per_interval[tick_key]
            #     elif len(input_offset_notes_per_interval[tick_key]) > len(style_offset_notes_per_interval[tick_key]):
            else:
                input_note_list = input_expand_notes_per_interval[tick_key]
                while len(input_note_list) > len(self.style_offset_notes_per_interval[tick_key]):
                    input_note_list_red = self.reduce_one_note(input_note_list, input_updated_chord_per_interval[tick_key])
                    if len(input_note_list_red) == len(input_note_list):
                        break
                    input_note_list = input_note_list_red
                input_transfer_notes_per_interval[tick_key] = input_note_list
            prev_tick_key = tick_key
        return input_transfer_notes_per_interval

    def get_style_input_note_map(self, input_transfer_notes_per_interval, style_populated_chord_per_interval,
                                 input_updated_chord_per_interval):
        style_input_note_map = dict()
        for tick_key in self.style_offset_notes_per_interval:

            #     print(tick_key)
            one_dict = dict()

            style_notes = sorted(self.style_offset_notes_per_interval[tick_key])

            if len(style_notes) == 0:
                style_input_note_map[tick_key] = one_dict
                continue

            input_notes = sorted(input_transfer_notes_per_interval[tick_key])
            style_chord = style_populated_chord_per_interval[tick_key]
            input_chord = input_updated_chord_per_interval[tick_key]

            style_chord_note = int(style_chord.split("_", 1)[0])
            # print(tick_key)
            input_chord_note = int(input_chord.split("_", 1)[0])

            if style_chord_note in style_notes and input_chord_note in input_notes:
                one_dict[style_chord_note] = input_chord_note

                i = style_notes.index(style_chord_note)
                j = input_notes.index(input_chord_note)
            else:
                i = 0
                j = 0
                one_dict[style_notes[0]] = input_notes[0]

            for k in range(max(len(style_notes), len(input_notes))):
                i += 1
                i %= len(style_notes)
                j += 1
                j %= len(input_notes)
                one_dict[style_notes[i]] = input_notes[j]

            style_input_note_map[tick_key] = one_dict.copy()
        return style_input_note_map

    def get_style_key_nos(self):
        style_key_nos = {}

        for item in self.section_list:
            start_tick = TimeUtils.bar_beat_to_tick(item["input_start_beat"], self.time_sig, ticks_per_beat)
            end_tick = TimeUtils.bar_beat_to_tick(item["input_end_beat"], self.time_sig, ticks_per_beat)

            keys = [x for x in range(start_tick, end_tick) if x % 480 == 0]
            # print(keys)

            scale_key = item["style_key"]
            key_nos = self.get_key_nos(scale_key)

            for key in keys:
                style_key_nos[key] = key_nos
        return style_key_nos

    def get_note_offset_from_note_no(self, key_nos, note_no):
        return note_no - Commons.highest_smaller(key_nos, note_no)

    def find_nearest_value(self, lst, target):
        return min(lst, key=lambda x: abs(x - target))

    def get_note_no_from_note_offset(self, key_nos, offset_no, reference):
        offset_nos = [x + offset_no for x in key_nos]
        return self.find_nearest_value(offset_nos, reference)

    def update_note_number(self, original_message, new_note):
        # Create a new MIDI message with the same type, velocity, and time as the original message
        updated_message = Message(original_message.type, note=new_note, velocity=original_message.velocity,
                                  time=original_message.time)
        return updated_message

    def break_midi_based_on_input_chord_change(self, midi, input_next_chord_change_marker, style_next_chord_change_marker):
        new_aligned_style_midi = mido.MidiFile(ticks_per_beat=ticks_per_beat)
        chord_change_marker_list = list(input_next_chord_change_marker.keys())
        for index, tick_key in enumerate(chord_change_marker_list):
            next_input_chord_change = input_next_chord_change_marker[tick_key]
            next_style_chord_change = style_next_chord_change_marker[tick_key]
            if next_input_chord_change == True and next_style_chord_change == False:
                        midi = MidiUtils.break_midi(midi, chord_change_marker_list[index+1])
        return midi

    def transfer_midi(self, style_key_nos, style_input_note_map, input_key_nos):
        transferred_midi = mido.MidiFile(ticks_per_beat=ticks_per_beat)
        # print(style_key_nos)
        for track in self.aligned_style_midi.tracks:
            track_name = track.name if track.name else 'Unnamed Track'
            if "drum" in track_name.lower():
                # pdb.set_trace()
                transferred_midi.tracks.append(track)
                continue
            new_track = mido.MidiTrack()
            abs_time = 0
            note_on_dict = dict()
            for msg in track:
                abs_time += msg.time
                type = msg.type
                if msg.type == 'note_on' and msg.note in note_on_dict:
                    type = 'note_off'

                if type == 'note_on':
                    tick_key = ticks_per_beat * int(abs_time / ticks_per_beat)
                    #             print(tick_key)
                    style_note = int(msg.note)

                    style_offset = self.get_note_offset_from_note_no(style_key_nos[tick_key], style_note)
                    if not tick_key in style_input_note_map:
                        print("Error::"+str(tick_key))
                        continue
                    if style_offset not in style_input_note_map[tick_key]:
                        # print(track_name + ":" + str(tick_key) + "  :  " + str(style_offset))
                        continue
                    input_offset = style_input_note_map[tick_key][style_offset]
                    input_note = self.get_note_no_from_note_offset(input_key_nos, input_offset, style_note)
                    #             print(input_note)
                    note_on_dict[style_note] = input_note
                    new_msg = Message(type, note=input_note, velocity=msg.velocity, time=msg.time)

                elif type == 'note_off' and msg.note in note_on_dict:
                    new_msg = Message(type, note=note_on_dict[msg.note], velocity=msg.velocity, time=msg.time)
                    note_on_dict.pop(msg.note)

                else:
                    new_msg = msg.copy()
                new_track.append(new_msg)
            transferred_midi.tracks.append(new_track)
        return transferred_midi

    def route(self):
        print("Applying reference to MIDI...")
        input_populated_chord_per_interval = self.populate_missing_chords(self.input_chord_per_interval)
        style_populated_chord_per_interval = self.populate_missing_chords(self.style_chord_per_interval)
        # print(style_populated_chord_per_interval)
        input_next_chord_change_marker = self.get_next_chord_change_marker(input_populated_chord_per_interval)
        style_next_chord_change_marker = self.get_next_chord_change_marker(style_populated_chord_per_interval)

        input_expand_notes_per_interval = self.expand_notes_per_interval(input_next_chord_change_marker)
        input_key_nos = self.get_key_nos(self.input_key)
        input_transfer_notes_per_interval = self.get_transfer_notes_per_interval(input_expand_notes_per_interval,
                                                                                 input_populated_chord_per_interval)
        style_input_note_map = self.get_style_input_note_map(input_transfer_notes_per_interval, style_populated_chord_per_interval, input_populated_chord_per_interval)
        style_key_nos = self.get_style_key_nos()
        # print(style_key_nos)
        # print(input_key_nos)
        # print(style_input_note_map)
        # print(self.aligned_style_midi)
        self.aligned_style_midi = self.break_midi_based_on_input_chord_change(self.aligned_style_midi, input_next_chord_change_marker, style_next_chord_change_marker)
        transferred_midi = self.transfer_midi(style_key_nos, style_input_note_map, input_key_nos)

        return transferred_midi





