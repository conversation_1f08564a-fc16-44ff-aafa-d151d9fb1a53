import json
from typing import Dict, List, Any, Union
import rpp


class ReaperProjectConverter:
    """Converter class for REAPER project data structures to JSON"""

    def __init__(self):
        self.indent = 2

    def element_to_dict(self, element) -> Dict[str, Any]:
        result = {
            "tag": element.tag,
            "attributes": element.attrib if hasattr(element, 'attrib') else [],
            "children": []
        }

        if hasattr(element, 'children') and element.children:
            for child in element.children:
                if isinstance(child, list):
                    # Handle list items (like ['RIPPLE', '0', '0'])
                    result["children"].append(child)
                elif hasattr(child, 'tag'):
                    result["children"].append(self.element_to_dict(child))
                else:
                    # Handle other data types
                    result["children"].append(child)

        return result

    def convert_reaper_data(self, data: Any) -> Any:
        """Recursively convert REAPER data structure to JSON-serializable format"""
        if hasattr(data, 'tag'):
            return self.element_to_dict(data)
        elif isinstance(data, list):
            # Handle lists recursively
            return [self.convert_reaper_data(item) for item in data]
        elif isinstance(data, dict):
            # Handle dictionaries recursively
            return {key: self.convert_reaper_data(value) for key, value in data.items()}
        else:
            # Handle primitive types (strings, numbers, etc.)
            return data

    def to_json_string(self, data: Any, pretty_print: bool = True) -> str:
        """Convert data to JSON string"""
        converted_data = self.convert_reaper_data(data)
        if pretty_print:
            return json.dumps(converted_data, indent=self.indent, ensure_ascii=False)
        else:
            return json.dumps(converted_data, ensure_ascii=False)

    def to_json_file(self, data: Any, filename: str, pretty_print: bool = True) -> bool:
        """Save data as JSON file"""
        try:
            converted_data = self.convert_reaper_data(data)
            with open(filename, 'w', encoding='utf-8') as f:
                if pretty_print:
                    json.dump(converted_data, f, indent=self.indent,
                              ensure_ascii=False)
                else:
                    json.dump(converted_data, f, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving to file: {e}")
            return False

# Simple function for quick conversion


def reaper_dict_to_json(reaper_data: Any, pretty_print: bool = True) -> str:
    """
    Quick function to convert REAPER data structure to JSON string

    Args:
        reaper_data: The REAPER project data structure
        pretty_print: Whether to format the JSON with indentation

    Returns:
        JSON string representation of the data
    """
    converter = ReaperProjectConverter()
    return converter.to_json_string(reaper_data, pretty_print)


def save_reaper_json(reaper_data: Any, filename: str, pretty_print: bool = True) -> bool:
    """
    Save REAPER data structure as JSON file

    Args:
        reaper_data: The REAPER project data structure
        filename: Output filename
        pretty_print: Whether to format the JSON with indentation

    Returns:
        True if successful, False otherwise
    """
    converter = ReaperProjectConverter()
    return converter.to_json_file(reaper_data, filename, pretty_print)


# Example usage with sample data structure similar to your REAPER project
if __name__ == "__main__":
    # class Element:
    #     def __init__(self, tag, attrib=None, children=None):
    #         self.tag = tag
    #         self.attrib = attrib or []
    #         self.children = children or []

    # Create sample data structure similar to your REAPER project
    with open("/Users/<USER>/melodyze-org/style-manager-app/backend/rpp_parser_output/parsed.rpp", "r") as f:
        sample_reaper_data = rpp.load(f)

    # Convert to JSON
    print("=== Converting REAPER Project Data to JSON ===")

    # Method 1: Quick conversion to string
    json_string = reaper_dict_to_json(sample_reaper_data)
    print("JSON String (first 500 characters):")
    print(json_string[:500] + "..." if len(json_string) > 500 else json_string)
    print()

    # Method 2: Save to file
    success = save_reaper_json(sample_reaper_data, "reaper_project.json")
    print(f"Saved to file: {success}")

    # Method 3: Using the converter class directly
    converter = ReaperProjectConverter()

    # Compact JSON (no indentation)
    compact_json = converter.to_json_string(
        sample_reaper_data, pretty_print=False)
    print(f"Compact JSON length: {len(compact_json)} characters")

    # Pretty printed JSON
    pretty_json = converter.to_json_string(
        sample_reaper_data, pretty_print=True)
    print("Pretty JSON (showing structure):")
    lines = pretty_json.split('\n')
    for i, line in enumerate(lines[:20]):  # Show first 20 lines
        print(line)
    if len(lines) > 20:
        print("... (truncated)")
