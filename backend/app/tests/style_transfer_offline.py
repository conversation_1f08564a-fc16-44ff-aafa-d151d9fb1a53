import os
import sys
import time
from bson.objectid import ObjectId
from mido import MidiFile

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.daw_modules.chord_identification import ChordIdentifier
from app.daw_modules.rpp_combining import Rpp<PERSON><PERSON>iner
from app.utilities.db import mongoDB
from app.style_transfer.final_transfer import MIDI_Transfer
from app.utilities.s3 import AWS_S3, styles_bucket
from app.daw_modules.style_alignment import Aligner
from app.utilities.midi_utils import MidiUtils
from app.utilities.rpp_utils import RppUtils
from app.utilities.time_utils import TimeUtils
from app.utilities.file_utils import FileUtils




def style_transfer():
    print(f"cwd: {os.getcwd()}")
    os.chdir(f"{os.getcwd()}/backend")
    session_id = TimeUtils.datetime_str()
    temp_dir = FileUtils.temp_dir(f"transfer_{session_id}")
    # Inputs
    section_items = [
        {
            "section": "intro",
            "input_start_beat": "1.1",
            "input_end_beat": "5.1",
            "style_midi": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\trimmed_midi_intro.mid",
            "style_rpp": "D:\\Melodyze\Style_Cuts\\paradise-guitar\\intro\\trimmed_rpp_intro.rpp",
            "style_key": "F",
            "project_id": "project_1"
        },
        {
            "section": "verse 1",
            "input_start_beat": "5.1",
            "input_end_beat": "13.1",
            "style_midi": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\trimmed_midi_verse.mid",
            "style_rpp": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\verse\\trimmed_rpp_verse.rpp",
            "style_key": "F",
            "project_id": "project_1"
        },
        {
            "section": "verse 2",
            "input_start_beat": "13.1",
            "input_end_beat": "21.1",
            "style_midi": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\trimmed_midi_verse.mid",
            "style_rpp": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\verse\\trimmed_rpp_verse.rpp",
            "style_key": "F",
            "project_id": "project_1"
        },
        {
            "section": "bridge 1",
            "input_start_beat": "21.1",
            "input_end_beat": "33.1",
            "style_midi": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\trimmed_midi_pre-chorus.mid",
            "style_rpp": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\pre-chorus\\trimmed_rpp_pre-chorus.rpp",
            "style_key": "F",
            "project_id": "project_1"
        },
        {
            "section": "chorus",
            "input_start_beat": "33.1",
            "input_end_beat": "42.1",
            "style_midi": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\trimmed_midi_chorus.mid",
            "style_rpp": "D:\\Melodyze\\Style_Cuts\\paradise-guitar\\chorus\\trimmed_rpp_chorus.rpp",
            "style_key": "F",
            "project_id": "project_1"   
        }
        
    ]

    input_name = "hey_jude"
    input_midi_file = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Hey Jude\\Hey Jude ROCK Midi\\Hey Jude ROCK Midi.mid"
    input_rpp_file = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Hey Jude\\Hey Jude ROCK Reaper\\Hey Jude ROCK.RPP"
    input_tempo = 85
    input_key = "F#"
    time_sig = 4
    input_genre = "Rock"

    input_midi = MidiFile(input_midi_file)
    input_midi = MidiUtils.curate_midi(input_midi)
    # print(input_midi)

    output_path = "D:\\Melodyze\\st_output"
    final_output_dir = os.path.join(output_path, f"{input_name}_{session_id}")
    final_output_dir = r'\\?\\' + final_output_dir
    os.makedirs(final_output_dir, exist_ok=True)


    rpp_combiner = RppCombiner(output_path, input_name, input_genre, input_tempo, section_items)
    output_rpp_file_path = rpp_combiner.router(final_output_dir)

    aligner = Aligner(time_sig, input_midi, input_tempo, section_items)
    aligned_midi = aligner.route(temp_dir)
    aligned_midi.save(".\\test\\aligned_style.mid")
    # aligned_midi.save(transferred_midi_path)

    print("Opening Reaper...")
    RppUtils.open_reaper()

    chord_identifier = ChordIdentifier(time_sig, input_midi, input_key, aligned_midi, section_items)
    input_chord, input_offset, input_notes, style_chord, style_offset, style_notes = chord_identifier.router(input_rpp_file, output_rpp_file_path)
    # print(style_notes)

    midi_transfer = MIDI_Transfer(time_sig, input_chord, input_offset, input_notes, style_chord, style_offset, style_notes,
                                  input_key, aligned_midi, section_items)
    transferred_midi = midi_transfer.route()
    print("\nMIDI Transfer completed!!!")
    transferred_midi_path = os.path.join(output_path, "transferred.mid")
    transferred_midi.save(transferred_midi_path)
    print(f"Output path: {transferred_midi_path}")
    
    print("Importing MIDI into Reaper.")
    print("********** Go To Reaper to see the progress and listen to the output. **********")
    MidiUtils.import_midi_into_reaper(output_rpp_file_path, transferred_midi_path, temp_dir)
    print("Done")


def style_upload():
    try:
        # TODO: pending field {created_by: ObjectId()}  //user_id
        now = int(time.time_ns())
        temp_download_dir = FileUtils.temp_dir(f"upload_{now}")

        data_dict = {
            "time_signature": "4_by_4",
            "swing": False,
            "genre": "Guitar",
            "pitch": "F",
            "scale": "major",
            "tempo": "140",
            "source": "Paradise_temp",
            "annotator": "Ayon"
        }

        midi_zip_file_path = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Paradise Guitar for Style Transfer\\Paradise Guitar Midi.zip"
        audio_file_path = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Paradise Guitar for Style Transfer\\Paradise Guitar KARAOKE F 140 4_4.mp3"
        audio_with_vocals_file_path = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Paradise Guitar for Style Transfer\\Paradise Guitar REF F 140 4_4.mp3"
        rpp_zip_file_path = "D:\\Melodyze\\Style_Transfer_Files\\Styles\\Paradise Guitar for Style Transfer\\Paradise Guitar Reaper.zip"

        midi_folder_path = midi_zip_file_path.replace(".zip", "")
        midi_file_path = FileUtils.unzip_file(midi_zip_file_path, midi_folder_path, "mid") 
        
        rpp_folder_path = rpp_zip_file_path.replace(".zip", "")
        rpp_file_path = FileUtils.unzip_file(rpp_zip_file_path, rpp_folder_path, "rpp") 

        markers = MidiUtils.trim_midi_by_markers(midi_file_path, temp_download_dir, data_dict["time_signature"], data_dict["tempo"])
        markers = RppUtils.trim_audio_by_markers(audio_file_path, audio_with_vocals_file_path, temp_download_dir, markers)
        markers = RppUtils.trim_rpp_by_markers(rpp_file_path, temp_download_dir, markers)

        #TODO: create mster table and put raw info there
        styles_to_insert = []
        proj_ref_id = RppUtils.get_project_ref_id(os.path.basename(rpp_file_path))
        for marker in markers:
            style = data_dict.copy()
            style["section"] = MidiUtils.get_section_mapping(marker["name"])
            style_id = ObjectId()
            style["_id"] = style_id

            # zip trimmed midi and upload to s3
            trimmed_midi_zip_path = os.path.join(
                temp_download_dir, f"trimmed_midi_{marker['name']}.zip"
            )
            FileUtils.zip_file(marker["cut_midi_filepath"], trimmed_midi_zip_path)

            midi_zip_dest_key = f"midi/{style_id}.zip"
            AWS_S3.upload_file(trimmed_midi_zip_path, styles_bucket, midi_zip_dest_key)
            style["midi_zip_path"] = f"/{styles_bucket}/{midi_zip_dest_key}"

            # zip trimmed rpp and upload to s3
            trimmed_rpp_zip_path = os.path.join(
                temp_download_dir, f"trimmed_rpp_{marker['name']}.zip"
            )
            FileUtils.zip_file(marker["cut_rpp_filepath"], trimmed_rpp_zip_path)

            rpp_zip_dest_key = f"project/{style_id}.zip"
            AWS_S3.upload_file(trimmed_rpp_zip_path, styles_bucket, rpp_zip_dest_key)
            style["project_zip_path"] = f"/{styles_bucket}/{rpp_zip_dest_key}"

            # copy audio and upload to s3
            audio_dest_key = f"audio/{style_id}.mp3"
            AWS_S3.upload_file(marker["cut_audio_filepath"], styles_bucket, audio_dest_key)
            style["audio_path"] = f"/{styles_bucket}/{audio_dest_key}"

            audio_with_vocals_dest_key = f"audio_with_vocals/{style_id}.mp3"
            AWS_S3.upload_file(marker["cut_audio_with_vocals_filepath"], styles_bucket, audio_with_vocals_dest_key)
            style["audio_with_vocals_path"] = f"/{styles_bucket}/{audio_with_vocals_dest_key}"

            style["created_at"] = TimeUtils.timestamp_mills()
            style["project_ref_id"] = proj_ref_id
            styles_to_insert.append(style)

        if len(styles_to_insert) > 0:
            inserted_data = mongoDB.styles_master.insert_many(data=styles_to_insert)

        FileUtils.delete_dir(temp_download_dir)


    except Exception as e:
        print(f"error: upload_style: {str(e)}")
        FileUtils.delete_dir(temp_download_dir)

        

if __name__ == '__main__':
    style_transfer()
    # style_upload()
