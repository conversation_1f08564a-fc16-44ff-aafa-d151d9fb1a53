import sys, os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.twelve_scale_render.core import *

def curate_path(path):
    # Replace single backslashes with double backslashes but keep existing double backslashes intact
    # curated_path = re.sub(r'(?<!\\)\\(?!\\)', r'\\\\', path)

    curated_path = path

    # Remove leading and trailing quotes if they exist
    if curated_path.startswith(("'", '"')):
        curated_path = curated_path[1:]
    if curated_path.endswith(("'", '"')):
        curated_path = curated_path[:-1]

    return curated_path

config = {
        "default_scale": "B",
        "down_shift": int("-5"),
        "up_shift": int("6"),
        "project_path": curate_path("D:\\Melodyze\\twelve_render_files\\Azizam Guitar only 128 D#m 4 4.rpp"),
        "MIDI_path": curate_path("D:\\Melodyze\\twelve_render_files\\Azizam Guitar only 128 D#m 4 4 3.mid"),
        # "project_path": curate_path("D:\\Melodyze\\Style_Transfer_Files\\Styles\\Hey Jude\\Hey Jude ROCK Reaper\\Hey Jude ROCK.RPP"),
        # "MIDI_path": curate_path("D:\\Melodyze\\Style_Transfer_Files\\Styles\\Hey Jude\\Hey Jude ROCK Midi\\Hey Jude ROCK Midi.mid"),
        # "project_path": "/Users/<USER>/Downloads/Lovely Chill.rpp",
        # "MIDI_path":"/Users/<USER>/Downloads/Media/Lovely Chill_test.mid",
        "save_12_session": "Yes",
        "total_wait_time": int("0")
    }

if __name__ == "__main__":
    router(config)
