import re
import string
import random

class Commons:

    # String methods
    @staticmethod
    def normalize_string(s: str) -> str:
        normalized = re.sub(r"[^a-zA-Z0-9]", "_", s)
        return normalized.lower()

    @staticmethod
    def add_str_int(str_, int_):
        return str(int(str_) + int_)

    @staticmethod
    def get_random_string(length=5):
        characters = string.ascii_letters + string.digits
        return "".join(random.choice(characters) for _ in range(length))

    @staticmethod
    def search_word_in_string(word, string):
        # Create a regular expression pattern to match the exact word with any non-word characters around it
        pattern = r"(?<![a-zA-Z])" + re.escape(word) + r"(?![a-zA-Z])"
        # Search for the pattern in the string (case-insensitive)
        match = re.search(pattern, string, re.IGNORECASE)
        return bool(match)

    @staticmethod
    def levenshtein_distance(s1, s2):
        """
        Compute the Levenshtein distance between two strings s1 and s2.
        This measures the minimum number of single-character edits required to change s1 into s2.
        """
        m, n = len(s1), len(s2)
        # Create a distance matrix and initialize it.
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j

        # Fill in the matrix.
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i - 1] == s2[j - 1]:
                    cost = 0
                else:
                    cost = 1
                dp[i][j] = min(
                    dp[i - 1][j] + 1,  # deletion
                    dp[i][j - 1] + 1,  # insertion
                    dp[i - 1][j - 1] + cost,  # substitution
                )
        return dp[m][n]

    @staticmethod
    def approximate_substring_match(text, pattern, max_distance):
        """
        Check if the pattern approximately appears in the text.
        We slide over the text with a window of size equal to len(pattern)
        and calculate the Levenshtein distance for each substring.
        If any substring's distance is less than or equal to max_distance, we consider it a match.
        Returns True if a match is found along with the starting index and the corresponding distance.
        """
        m = len(pattern)
        n = len(text)

        if m > n:
            # If pattern is longer than text, just compare the two.
            distance = Commons.levenshtein_distance(pattern, text)
            return (
                distance <= max_distance,
                0 if distance <= max_distance else -1,
                distance,
            )

        best_distance = max_distance + 1
        best_index = -1

        # Slide the window over the text.
        for i in range(n - m + 1):
            substring = text[i: i + m]
            dist = Commons.levenshtein_distance(pattern, substring)
            if dist < best_distance:
                best_distance = dist
                best_index = i
                # Early exit if we have an exact match.
                if best_distance == 0:
                    break

        return (best_distance <= max_distance, best_index, best_distance)

    @staticmethod
    def highest_smaller(numbers, target):
        # Filtering numbers that are less than the target
        if len(numbers) == 0:
            return target
        smaller_numbers = [n for n in numbers if n <= target]
        # Return the max number if the list isn't empty, otherwise return None
        return max(smaller_numbers) if smaller_numbers else None
