import json
from typing import Any, Optional, TypeVar, Generic, Union, Dict, List, Type
import certifi
from bson import json_util
from bson.objectid import ObjectId
from pydantic import BaseModel, Field
from pymongo.mongo_client import MongoClient
from pymongo.collection import Collection
from app.env import env
from app.utilities.logger import logger

mongoClient = MongoClient(env.MONGODB_URL, tlsCAFile=certifi.where())
db = mongoClient["song_style"]

# Type variable for generic collection types
T = TypeVar("T", bound=BaseModel)


class OriginalStyleTable(BaseModel):
    id: str = Field(alias="_id")  # Changed to str since replace_oid converts ObjectId to string
    time_signature: str
    swing: bool
    genre: str
    pitch: str
    scale: str
    tempo: str
    source: Optional[str]
    project_zip_path: str
    audio_path: str
    audio_with_vocals_path: str
    midi_zip_path: str
    project_file_name: str
    # project_ref_id: Optional[str]
    annotator: str
    created_at: int
    created_by: str


class ProcessedStyleTable(BaseModel):
    id: str = Field(alias="_id")  # Changed to str since replace_oid converts ObjectId to string
    time_signature: str
    swing: bool
    genre: str
    pitch: str
    scale: str
    tempo: str
    source: Optional[str]
    audio_path: str
    audio_with_vocals_path: str
    midi_zip_path: str
    project_zip_path: str
    project_file_name: str
    annotator: str
    created_at: int
    section: str
    raw_style_id: str
    duration_in_bars: int
    project_ref_id: str


class TransferStyleTable(BaseModel):
    id: str = Field(alias="_id")
    time_signature: str
    swing: bool
    genre: str
    pitch: str
    scale: str
    tempo: str
    input_midi_path: str
    song_name: str
    project_ref_id: str
    sections: list[Any]
    output_directory: str
    created_at: int
    output_midi_zip_path: str
    output_rpp_zip_path: str


def connectMongo() -> None:
    logger.info("connecting MongoDB")
    try:
        cmd = db.command({"ping": 1})
        logger.info(f"MongoDB successfully connected: {cmd}")
    except Exception as e:
        logger.error(f"MongoDB connection failed: {str(e)}")


def replace_oid(data: Any) -> Any:
    """
    Recursively replace MongoDB ObjectId with string representation
    """
    if isinstance(data, list):
        return [replace_oid(item) for item in data]
    elif isinstance(data, dict):
        if "$oid" in data:
            return str(data["$oid"])
        return {key: replace_oid(value) for key, value in data.items()}
    else:
        return data


class MongoHelper(Generic[T]):
    def __init__(self, coll: Collection, model_class: Type[T]):
        self.coll = coll
        self.model_class = model_class

    def _parse_result(self, result: Any) -> Optional[T]:
        """Parse single result to model type"""
        if result is None:
            return None
        cleaned_data = replace_oid(json.loads(json_util.dumps(result)))
        return self.model_class(**cleaned_data)

    def _parse_results(self, results: List[Any]) -> List[T]:
        """Parse multiple results to model types"""
        cleaned_data = replace_oid(json.loads(json_util.dumps(results)))
        return [self.model_class(**item) for item in cleaned_data]

    def find(
        self,
        filter: Dict[str, Any],
        projection: Optional[Dict[str, Any]] = None,
        options: Optional[Dict[str, Any]] = None,
    ) -> List[T]:
        """Find multiple documents and return typed results"""
        if options is None:
            options = {}

        skip = options.get("skip")
        limit = options.get("limit")
        sort_by = options.get("sort_by")
        sort_order = options.get("sort_order", 1)

        query = self.coll.find(filter, projection)
        if sort_by:
            query = query.sort(sort_by, sort_order)
        if skip:
            query = query.skip(skip)
        if limit:
            query = query.limit(limit)

        return self._parse_results(list(query))

    def find_by_id(
        self, id: str, projection: Optional[Dict[str, Any]] = None
    ) -> Optional[T]:
        """Find document by ID and return typed result"""
        query = self.coll.find_one({"_id": ObjectId(id)}, projection)
        return self._parse_result(query)

    def find_one(
        self, filter: Dict[str, Any], projection: Optional[Dict[str, Any]] = None
    ) -> Optional[T]:
        """Find single document and return typed result"""
        query = self.coll.find_one(filter, projection)
        return self._parse_result(query)

    def create(self, data: Union[T, Dict[str, Any]]) -> Any:
        """Create document and return typed result"""
        if isinstance(data, BaseModel):
            # Convert Pydantic model to dict, handle _id field
            data_dict = data.model_dump()
            if "_id" in data_dict and isinstance(data_dict["_id"], str):
                data_dict["_id"] = ObjectId(data_dict["_id"])
        else:
            data_dict = data

        result = self.coll.insert_one(data_dict)
        data_dict["_id"] = result.inserted_id
        return self._parse_result(data_dict)

    def insert_many(self, data: List[Union[T, Dict[str, Any]]]) -> List[T]:
        """Insert multiple documents and return typed results"""
        data_dicts = []
        for item in data:
            if isinstance(item, BaseModel):
                item_dict = item.model_dump()
                if "_id" in item_dict and isinstance(item_dict["_id"], str):
                    item_dict["_id"] = ObjectId(item_dict["_id"])
                data_dicts.append(item_dict)
            else:
                data_dicts.append(item)

        result = self.coll.insert_many(data_dicts)

        # Update the data with inserted IDs
        for i, inserted_id in enumerate(result.inserted_ids):
            data_dicts[i]["_id"] = inserted_id

        return self._parse_results(data_dicts)

    def update_one(self, filter: Dict[str, Any], update: Dict[str, Any]) -> Optional[T]:
        """Update single document and return updated document"""
        result = self.coll.find_one_and_update(filter, update, return_document=True)
        return self._parse_result(result)

    def update_by_id(self, id: str, update: Dict[str, Any]) -> Optional[T]:
        """Update document by ID and return updated document"""
        return self.update_one({"_id": ObjectId(id)}, update)

    def delete_many(self, filter: Dict[str, Any]):
        """Delete multiple documents"""
        return self.coll.delete_many(filter)

    def delete_one(self, filter: Dict[str, Any]):
        """Delete single document"""
        return self.coll.delete_one(filter)

    def delete_by_id(self, id: str):
        """Delete document by ID"""
        return self.coll.delete_one({"_id": ObjectId(id)})

    def distinct(self, key: str, query: Dict[str, Any]) -> List[Any]:
        """Get distinct values for a field"""
        items = self.coll.distinct(key, query)
        return [str(item) if isinstance(item, ObjectId) else item for item in items]

    def count_documents(self, filter: Dict[str, Any]) -> int:
        """Count documents matching filter"""
        return self.coll.count_documents(filter)


class MongoDB:
    def __init__(self):
        self.processed_styles = MongoHelper[ProcessedStyleTable](
            db["styles_master"], ProcessedStyleTable
        )
        self.original_styles = MongoHelper[OriginalStyleTable](
            db["original_styles"], OriginalStyleTable
        )
        self.transferred_styles = MongoHelper[TransferStyleTable](
            db["style_transfers"], TransferStyleTable
        )


# Create singleton instance
mongoDB = MongoDB()

# Usage Examples:
"""
# Now with proper typing support:

# Find operations return proper types
original_style: Optional[OriginalStyleTable] = mongoDB.original_styles.find_by_id(raw_style_id)
if original_style:
    print(original_style.genre)  # IDE will provide autocomplete
    print(original_style.tempo)  # Type checking works

# Find multiple with proper typing
styles: List[OriginalStyleTable] = mongoDB.original_styles.find(
    {"genre": "rock"}, 
    options={"limit": 10, "sort_by": "created_at", "sort_order": -1}
)

# Create with type safety
new_style = OriginalStyleTable(
    _id="",  # Will be set by MongoDB
    time_signature="4/4",
    swing=False,
    genre="jazz",
    scale="major",
    tempo="120",
    marker="A",
    source="manual",
    project_zip_path="/path/to/zip",
    audio_path="/path/to/audio",
    midi_zip_path="/path/to/midi",
    project_ref_id="proj123",
    created_at=1234567890,
    created_by="user123"
)

created_style: OriginalStyleTable = mongoDB.original_styles.create(new_style)

# Update operations
updated_style: Optional[OriginalStyleTable] = mongoDB.original_styles.update_by_id(
    style_id, 
    {"$set": {"tempo": "140"}}
)
"""
