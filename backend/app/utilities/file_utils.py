import zipfile
import os
import shutil
from app.utilities.logger import logger
from pathlib import Path


class FileUtils:

    @staticmethod
    def root_dir():
        # return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.getcwd()

    @staticmethod
    def zip_file(file_path, zip_path):
        # Check if the path is a file or directory
        if os.path.isfile(file_path):
            # Handle single file
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add file to zip with its basename as the archive name
                zipf.write(file_path, os.path.basename(file_path))

        elif os.path.isdir(file_path):
            # Handle directory
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Get the parent directory and root folder name
                base_dir = os.path.basename(file_path)

                # Walk through the directory
                for root, dirs, files in os.walk(file_path):
                    # Add empty directories
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        # Calculate relative path including the root folder
                        arcname = os.path.join(
                            base_dir, os.path.relpath(dir_path, file_path))
                        # Add directory to zip (with trailing slash to indicate directory)
                        zipf.write(dir_path, arcname + '/')

                    # Add files
                    for file in files:
                        file_full_path = os.path.join(root, file)
                        # Calculate relative path including the root folder
                        arcname = os.path.join(
                            base_dir, os.path.relpath(file_full_path, file_path))
                        # Add file to zip
                        zipf.write(file_full_path, arcname)
        else:
            raise FileNotFoundError(f"The path {file_path} does not exist")

    @staticmethod
    def unzip_file(zip_filepath: str, extract_path: str, ext: str):
        with zipfile.ZipFile(zip_filepath, "r") as zip_ref:
            zip_ref.extractall(extract_path)

        all_files = []
        for root, dirs, files in os.walk(extract_path):
            for file in files:
                if file.lower().endswith(f'.{ext}'):
                    all_files.append(os.path.join(root, file))
        filepath = all_files[0]
        logger.info(f"Unzipped file path: {filepath}")
        return filepath

    @staticmethod
    def get_filename(filepath: str):
        path = Path(filepath)
        filename = path.stem
        ext = path.suffix
        return filename, ext

    @staticmethod
    def delete_dir(dir: str):
        try:
            shutil.rmtree(dir)
            logger.info(f"Folder '{dir}' deleted successfully.")
        except FileNotFoundError:
            logger.error(f"Folder '{dir}' not found.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")

    @staticmethod
    def temp_dir(sub_dir: str = "") -> str:
        temp_dir = (
            os.path.join(FileUtils.root_dir(), "temp_dir", sub_dir)
            if sub_dir
            else os.path.join(FileUtils.root_dir(), "temp_dir")
        )
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        return temp_dir
