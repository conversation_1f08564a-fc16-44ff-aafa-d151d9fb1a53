import logging
import os
import sys

log_path = os.path.join(os.getcwd(), "app.log")

file_handler = logging.FileHandler(log_path, encoding="utf-8")
stream_handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter(
    "%(asctime)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
)

file_handler.setFormatter(formatter)
stream_handler.setFormatter(formatter)

logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, stream_handler],
)

logger = logging.getLogger(__name__)

