import os
import time
import mido
from mido import Message, MetaMessage, MidiFile, MidiTrack
from app.utilities.time_utils import TimeUtils
from app.utilities.file_utils import FileUtils
from app.utilities.commons import Commons
from app.constants.constants import target_ticks_per_beat, time_sign_map
from constants.constants import section_list

class MidiUtils:
    
    @staticmethod
    def get_mid_len(mid):
        track_wise_time = []
        for track in mid.tracks:
            abs_time = 0
            non_meta_flag = False
            for msg in track:
                if not msg.is_meta and non_meta_flag is False:
                    non_meta_flag = True
                abs_time += msg.time
            if non_meta_flag is True:
                track_wise_time.append(abs_time)
        if len(track_wise_time) == 0:
            return 0
        return max(track_wise_time)


    @staticmethod
    def curate_midi(input_midi):
        input_midi = MidiUtils.change_ticks_per_beat(input_midi, target_ticks_per_beat)
        input_len = MidiUtils.get_mid_len(input_midi)
        new_midi = MidiFile(ticks_per_beat=input_midi.ticks_per_beat)
        for track in input_midi.tracks:
            new_track = MidiTrack()
            note_on_set = set()
            abs_time = 0
            msg_list = []
            msg_list_ind = 0
            for msg in track:
                abs_time += msg.time
                new_msg = msg.copy()
                if msg.type == "note_on":
                    flag = False
                    for item in note_on_set:
                        if item[0] == msg.note:
                            flag = True
                    if flag:
                        new_msg = mido.Message(
                            "note_off",
                            note=msg.note,
                            velocity=msg.velocity,
                            time=msg.time,
                        )
                    if not flag and msg.velocity == 0:
                        new_msg = mido.Message(
                            "note_off",
                            note=msg.note,
                            velocity=msg.velocity,
                            time=msg.time,
                        )

                if new_msg.type == "note_on" and new_msg.velocity > 0:
                    note_on_set.add((msg.note, msg_list_ind, abs_time))

                del_item = None
                if new_msg.type == "note_off":
                    for item in note_on_set:
                        if item[0] == msg.note:
                            del_item = item
                    if del_item is not None:
                        note_on_set.remove(del_item)

                msg_list.append(new_msg)
                msg_list_ind += 1

            # If there is some note on msg left then replace it with note off
            flag = False
            for i, (note, ind, note_abs_time) in enumerate(note_on_set):
                print(f"{i} - {note}")
                msg = msg_list[ind]
                if note_abs_time >= input_len:
                    new_msg = Message(
                        "note_off",
                        note=msg.note,
                        velocity=msg.velocity,
                        time=msg.time,
                    )
                    msg_list[ind] = new_msg
                else:
                    if flag is False:
                        new_msg = Message(
                            "note_off",
                            note=msg.note,
                            velocity=msg.velocity,
                            time=input_len - abs_time,
                        )
                        msg_list.append(new_msg)
                        flag = True
                    else:
                        new_msg = Message(
                            "note_off",
                            note=msg.note,
                            velocity=msg.velocity,
                            time=0,
                        )
                        msg_list.append(new_msg)

            for msg in msg_list:
                new_track.append(msg)
            new_midi.tracks.append(new_track)
        return new_midi


    @staticmethod
    def concatenate_midi(midi1, midi2, same_track_flag, offset=0):
        new_midi = mido.MidiFile(ticks_per_beat=target_ticks_per_beat)
        midi1_len = MidiUtils.get_mid_len(midi1)
        midi2_len = MidiUtils.get_mid_len(midi2)

        if same_track_flag == False:
            for track in midi1.tracks:
                new_midi.tracks.append(track.copy())
            for track in midi2.tracks:
                new_track = mido.MidiTrack()
                for index, msg in enumerate(track):
                    new_msg = msg.copy()
                    if index == 0:
                        new_msg.time += MidiUtils.get_mid_len(midi1) + offset
                    new_track.append(new_msg)
                new_midi.tracks.append(new_track)
                        # print(new_msg.time)
        elif same_track_flag and len(midi1.tracks) == len(midi2.tracks):
            for i, track1 in enumerate(midi1.tracks):
                new_track = mido.MidiTrack()
                track2 = midi2.tracks[i]

                abs_time = 0
                for msg in track1:
                    new_track.append(msg)
                    abs_time += msg.time

                for index, msg in enumerate(track2):
                    new_msg = msg.copy()
                    if index == 0:
                        new_msg.time += max(0, MidiUtils.get_mid_len(midi1) - abs_time) + offset
                    new_track.append(new_msg)
                new_midi.tracks.append(new_track)
        elif same_track_flag and len(midi1.tracks) != len(midi2.tracks):
            # Combine tracks from the first MIDI
            used_track_2_ind = []
            for track in midi1.tracks:
                track_name = track.name if track.name else ""
                new_track = mido.MidiTrack()
                abs_time = 0
                for msg in track:
                    new_track.append(msg)
                    abs_time += msg.time

                # If there is a same name track under midi2 then include those msg
                for i, track2 in enumerate(midi2.tracks):
                    track2_name = track2.name if track2.name else ""
                    if (
                        track2_name == track_name
                        and len(track2_name) > 0
                        and i not in used_track_2_ind
                    ):
                        for index, msg in enumerate(track2):
                            new_msg = msg.copy()
                            if index == 0:
                                new_msg.time += max(
                                    0, MidiUtils.get_mid_len(midi1) - abs_time
                                ) + offset
                            new_track.append(new_msg)
                        used_track_2_ind.append(i)
                        break

                new_midi.tracks.append(new_track)

            # Adjust timestamps of events in tracks from the second MIDI file
            for i, track in enumerate(midi2.tracks):
                if i in used_track_2_ind:
                    continue
                new_track = mido.MidiTrack()
                flag = False
                for msg in track:
                    new_msg = msg.copy()
                    if flag is False:
                        # print(msg.time)
                        # print(midi1_len)
                        new_msg.time += MidiUtils.get_mid_len(midi1)
                        # print(new_msg.time)
                        flag = True
                    new_track.append(new_msg)
                new_midi.tracks.append(new_track)
        return new_midi
     

    @staticmethod
    def break_midi(midi, break_tick):
        epsilon = midi.ticks_per_beat / 8
        new_midi = mido.MidiFile(ticks_per_beat=midi.ticks_per_beat)
        for track in midi.tracks:
            new_track = mido.MidiTrack()
            prev_time = 0
            abs_time = 0
            note_dict = {}
            for msg in track:
                prev_time = abs_time
                abs_time += msg.time
                if msg.type == "note_on":
                    start_time = abs_time
                    note_dict[msg.note] = (start_time, None, msg)
                elif msg.type == "note_off":
                    if msg.note in note_dict:
                        start_time = note_dict[msg.note][0]
                        end_time = abs_time
                        note_on_msg = note_dict[msg.note][2]
                        note_dict[msg.note] = (start_time, end_time, note_on_msg)

            break_note_dict = {}
            for note in note_dict:
                start_time = note_dict[note][0]
                end_time = note_dict[note][1]
                if start_time < break_tick - epsilon and end_time > break_tick + epsilon:
                    break_note_dict[note] = (start_time, end_time)

            break_handle_flag = False
            prev_time = 0
            abs_time = 0
            for msg in track:
                prev_time = abs_time
                abs_time += msg.time
                if abs_time <= break_tick:
                    new_track.append(msg.copy(time=abs_time - prev_time))
                elif break_handle_flag is True:
                    new_track.append(msg.copy(time=abs_time - prev_time))
                else:
                    for index, note in enumerate(break_note_dict.keys()):
                        saved_msg = note_dict[note][2]
                        if index == 0:
                            delta = break_tick - prev_time
                            new_msg = Message("note_off", note=saved_msg.note, velocity=saved_msg.velocity, time=delta)
                            new_track.append(new_msg)
                            new_msg = Message("note_on", note=saved_msg.note, velocity=saved_msg.velocity, time=0)
                            new_track.append(new_msg)
                        else:
                            new_msg = Message("note_off", note=saved_msg.note, velocity=saved_msg.velocity, time=0)
                            new_track.append(new_msg)
                            new_msg = Message("note_on", note=saved_msg.note, velocity=saved_msg.velocity, time=0)
                            new_track.append(new_msg)
                    new_track.append(msg.copy(time=abs_time - break_tick))
                    break_handle_flag = True
            new_midi.tracks.append(new_track)
        return new_midi
     
     
    @staticmethod 
    def cut_midi(midi, start_tick, end_tick):
            epsilon = midi.ticks_per_beat / 8
            new_midi = MidiFile(ticks_per_beat=midi.ticks_per_beat)
            for track in midi.tracks:
                current_time = 0
                prev_time = max(start_tick, 0)
                new_track = MidiTrack()
                # track_name = None

                note_on_set = set()
                for msg in track:
                    current_time += msg.time
                    if msg.type == "note_on":  # Message
                        if (max(start_tick - epsilon, 0)) <= current_time < end_tick - epsilon:
                            time_delta = max(current_time - prev_time, 0)
                            prev_time = current_time if current_time > prev_time else prev_time
                            new_track.append(msg.copy(time=time_delta))
                            if msg.type == "note_on":
                                note_on_set.add(msg.note)
                    elif msg.type == "note_off":
                        if (max(start_tick, 0)) < current_time <= end_tick:
                            if msg.note in note_on_set:
                                time_delta = current_time - prev_time
                                prev_time = current_time
                                new_track.append(msg.copy(time=time_delta))
                                note_on_set.remove(msg.note)
                    elif msg.type == 'end_of_track':
                        continue
                    else:
                        new_track.append(msg.copy(time=0))
                
                        
                for i, note in enumerate(note_on_set):
                    time = 0
                    if i == 0:
                        time = end_tick - prev_time
                    new_track.append(Message("note_off", note=note, velocity=0, time=time))
                if len(note_on_set) > 0:
                    new_track.append(MetaMessage('end_of_track', time=0))
                else:
                    new_track.append(MetaMessage('end_of_track', time=end_tick - prev_time))
                new_midi.tracks.append(new_track)

            return new_midi


    @staticmethod
    def repeat_midi(midi, num_repeats):
        # Create a new MIDI file
        midi_len = MidiUtils.get_mid_len(midi)
        new_midi = mido.MidiFile(ticks_per_beat=midi.ticks_per_beat)

        for track in midi.tracks:
            new_track = None
            for i in range(num_repeats):
                if i == 0:
                    new_track = mido.MidiTrack()
                else:
                    start_delta = midi_len - abs_time
                abs_time = 0
                for index, msg in enumerate(track):
                    abs_time += msg.time
                    if i > 0 and index == 0:
                        new_msg = msg.copy(time=start_delta)
                    else:
                        new_msg = msg.copy()
                    new_track.append(new_msg)
            new_midi.tracks.append(new_track)

        return new_midi


    @staticmethod
    def trim_midi_by_markers(
        midi_file_path: str, output_dir: str, time_signature, bpm, epsilon: int = 50
    ):
        """
        Trims a MIDI file into separate files based on marker regions using absolute tick times.
        Includes track names for better organization.
        """
        markers = MidiUtils.calculate_markers_with_bars_and_beats(midi_file_path, time_signature, bpm)["markers"]
        # print(markers)

        if not markers:
            print("No markers found in the MIDI file.")
            return []

        midi = MidiFile(midi_file_path)
        output_files = []

        for marker in markers:
            start_tick = marker["start_tick"]
            end_tick = marker["end_tick"]
            new_midi = MidiUtils.cut_midi(midi, start_tick, end_tick)

            # Save the new MIDI file with marker and track name
            output_filepath = os.path.join(output_dir, f"trimmed_midi_{marker['name']}")
            while os.path.exists(output_filepath + ".mid"):
                output_filepath = output_filepath + "_"
            output_filepath = output_filepath + ".mid"
            new_midi.save(output_filepath)
            marker["cut_midi_filepath"] = output_filepath
            output_files.append(marker.copy())

        return output_files
    
    
    @staticmethod
    def change_ticks_per_beat(input_midi, new_ticks):
        # Load the original MIDI file
        mid = input_midi
        old_ticks = mid.ticks_per_beat
        factor = new_ticks / old_ticks

        # Create a new MIDI file with the desired ticks per beat
        new_mid = mido.MidiFile()
        new_mid.ticks_per_beat = new_ticks

        # Process each track and adjust the time of each message
        for track in mid.tracks:
            new_track = mido.MidiTrack()
            for msg in track:
                # Scale delta time by the factor; rounding to the nearest integer
                if msg.time:
                    msg.time = int(round(msg.time * factor))
                new_track.append(msg)
            new_mid.tracks.append(new_track)

        return new_mid
    
    
    @staticmethod
    #TODO: include second info in the markers
    def calculate_markers_with_bars_and_beats(midi_file_path: str, time_signature: str, bpm: int):
        """
        Reads a MIDI file and returns a dictionary with:
        - 'markers': a list of dicts, each including:
            name, start_tick, end_tick, start_bar, start_beat, end_bar, end_beat
        - 'ticks_per_beat': from the MIDI file
        """
        # print(time_signature)
        beats_per_measure = time_sign_map[time_signature]
        midi_raw = MidiFile(midi_file_path)
        midi = MidiUtils.curate_midi(midi_raw)
        # midi = MidiFile(midi_file_path)
        ticks_per_beat = midi.ticks_per_beat
        markers = []
        max_time_per_track = []

        current_marker_name = None
        current_marker_start_tick = None

        for track in midi.tracks:
            current_time = 0  # track-local absolute time in ticks
            max_time = 0  # track-local maximum time

            for msg in track:
                current_time += msg.time
                if msg.type in ["note_on", "note_off", "control_change", "program_change"]:
                    if current_time > max_time:
                        max_time = current_time

                # If we encounter a time signature, update it
                if msg.type == "time_signature":
                    time_signature = (msg.numerator, msg.denominator)

                # If we encounter a marker
                elif msg.type == "marker":
                    # If there was a previous marker "open", close it now
                    if current_marker_name is not None:
                        # Close the last marker
                        markers[-1]["end_tick"] = current_time

                        # Also compute end bar/beat
                        end_bar, end_beat = TimeUtils.get_bar_beat(
                            current_time, ticks_per_beat, time_signature[0]  # numerator
                        )
                        markers[-1]["end_bar"] = end_bar
                        markers[-1]["end_beat"] = end_beat

                        markers[-1]["end_seconds"] = TimeUtils.bar_beat_to_seconds(end_bar, end_beat, bpm, beats_per_measure)

                    # Open a new marker
                    current_marker_name = MidiUtils.get_section_mapping(msg.text.lower().strip())
                    if MidiUtils.get_section_mapping(current_marker_name.lower()) != "stop":
                        current_marker_start_tick = current_time
                        start_bar, start_beat = TimeUtils.get_bar_beat(
                            current_time, ticks_per_beat, time_signature[0]
                        )

                        markers.append(
                            {
                                "name": current_marker_name,
                                "start_tick": current_marker_start_tick,
                                "end_tick": None,  # to be filled later
                                "start_bar": start_bar,
                                "start_beat": start_beat,
                                "start_seconds": TimeUtils.bar_beat_to_seconds(start_bar, start_beat, bpm, beats_per_measure),
                                "end_bar": None,
                                "end_beat": None,
                                "end_seconds": None,
                            }
                        )

            max_time_per_track.append(max_time)

        # After processing all tracks, if the last marker is still "open", close it using
        # the maximum time across all tracks
        if markers:
            last_marker = markers[-1]
            if last_marker["end_tick"] is None:
                final_tick = max(max_time_per_track)
                last_marker["end_tick"] = final_tick

                # Also compute end bar/beat
                end_bar, end_beat = TimeUtils.get_bar_beat(
                    final_tick, ticks_per_beat, time_signature[0]
                )
                last_marker["end_bar"] = end_bar
                last_marker["end_beat"] = end_beat
                last_marker["end_seconds"] = TimeUtils.bar_beat_to_seconds(end_bar, end_beat, bpm, beats_per_measure)

        return {"markers": markers, "ticks_per_beat": ticks_per_beat}

    @staticmethod
    def get_initial_midi_pos():
        import reapy
        import reapy.reascript_api as RPR

        initial_midi_position = None

        # Get the total number of media items in the project
        num_items = RPR.CountMediaItems(0)  # '0' is the current project

        # Loop through each media item
        for i in range(num_items):
            item = RPR.GetMediaItem(0, i)
            take = RPR.GetMediaItemTake(item, 0)  # Get the first take of the item

            source = RPR.GetMediaItemTake_Source(take)
            source_type = RPR.GetMediaSourceType(source, "", 512)

            track = RPR.GetMediaItemTake_Track(take)
            is_muted = RPR.GetMediaTrackInfo_Value(track, "B_MUTE")
            is_take_muted = RPR.GetMediaTrackInfo_Value(take, "B_MUTE")

            # Check if the take is MIDI
            if source_type[1] == "MIDI" and is_muted == 0.0 and is_take_muted == 0.0:
                # Get the position of this MIDI item
                item_position = RPR.GetMediaItemInfo_Value(item, "D_POSITION")

                # Update the initial MIDI position if it's the first MIDI item found
                # or if it's earlier than the previously found position
                if initial_midi_position is None or item_position < initial_midi_position:
                    initial_midi_position = item_position
        if initial_midi_position is not None:
            return initial_midi_position
        else:
            return 0
        
    @staticmethod
    def import_midi_into_reaper(project_rpp_filepath, output_midi_path, temp_download_dir):
        import reapy
        import reapy.reascript_api as RPR

        RPR.Main_openProject(project_rpp_filepath)
        time.sleep(5) # vst loading time
            
        initial_midi_pos = MidiUtils.get_initial_midi_pos()
        
        midi_file = MidiFile(output_midi_path)
        temp_midi_path = str(os.path.join(temp_download_dir, "temp.mid"))
        
        # Get the current project (the one that is currently open in REAPER)
        project = reapy.Project()
        RPR.GetSetProjectInfo(0, "PROJECT_SRATE", 48000.0, True)
        start_pct = 0.0
        end_pct = 1.0
        
        rpr_track_name_list = []
        for i, rpr_track in enumerate(project.tracks):
            rpr_track_name = rpr_track.name
            # if track name same then make it unique
            while (rpr_track_name in rpr_track_name_list):
                rpr_track_name = rpr_track_name + "_"
            rpr_track_name_list.append(rpr_track_name)

            mido_track_name_list = []
            for j, mido_track in enumerate(midi_file.tracks):
                mido_track_name: str = mido_track.name
                # if track name same then make it unique
                while (mido_track_name in mido_track_name_list):
                    mido_track_name = mido_track_name + "_"
                mido_track_name_list.append(mido_track_name)

                if not rpr_track_name == mido_track_name:
                    continue

                new_midi = mido.MidiFile(ticks_per_beat=midi_file.ticks_per_beat)
                new_midi.tracks.append(mido_track)
                new_midi.save(temp_midi_path)
                
                RPR.SetEditCurPos(initial_midi_pos, True, True)

                RPR.SetOnlyTrackSelected(rpr_track.id)
                midi_segments = len(rpr_track.items)
                for i in range(midi_segments):
                    rpr_track.items[0].delete()
                RPR.InsertMedia(temp_midi_path, 0)


        RPR.Main_SaveProject(project, False)
        # output_wav_path = os.path.join(temp_download_dir, "output.wav")
        # RPR.RenderFileSection(project_rpp_filepath, output_wav_path, start_pct, end_pct, 1)
        
    
    @staticmethod
    def get_section_mapping(marker: str):
        marker = marker.lower()
        item_distance = {}
        if "pre" in marker:
            pass
        for item in section_list:
            if isinstance(item, list):
                dist = 0
                for sub_item in item:
                    found, index, distance = Commons.approximate_substring_match(sub_item, marker, 3)
                    dist += distance
                item_distance["-".join(item)] = dist
            else:
                found, index, distance = Commons.approximate_substring_match(item, marker, 3)   
                item_distance[item] = distance
        min_dist = min(item_distance.values())
        for item, dist in item_distance.items():
            if dist == min_dist:
                return item
        return "unknown"
