import os
import shutil
import re
import librosa
import soundfile as sf
import platform
import subprocess
import time
from app.utilities.commons import Commons

class RppUtils:

    @staticmethod
    def open_reaper(vst_loading_sec: int = 5):
        system = platform.system()
        reaper_path = None

        if system == "Windows":
            reaper_path = r"C:\Program Files\REAPER (x64)\reaper.exe"
        elif system == "Darwin":
            reaper_path = "/Applications/REAPER.app/Contents/MacOS/REAPER"
        else:
            raise NotImplementedError("Unsupported OS")

        if not os.path.exists(reaper_path):
            raise FileNotFoundError(f"REAPER not found at {reaper_path}")
        subprocess.Popen([reaper_path], shell=True)

        print("REAPER opened")
        time.sleep(vst_loading_sec)
        return True
    
    @staticmethod
    def get_project_ref_id(filename:str) -> str:
        # return "".join(random.choices(string.ascii_uppercase + string.digits, k=7))
        lower_case = filename.lower()
        formatted_string = re.sub(r'[^a-z0-9]', '_', lower_case)
        random_string = Commons.get_random_string()
        return f"{formatted_string}_{random_string}"

    
    @staticmethod
    def find_chunks(lines, tag="<TRACK"):
        """
        Locate and extract all blocks starting with a tag (e.g., <TRACK), 
        correctly handling nested structures.
        """
        chunks = []
        inside_chunk = False
        stack = []
        current_chunk = []

        for line in lines:
            stripped = line.strip()

            # Detect opening tag
            if stripped.startswith(tag):
                if inside_chunk:
                    stack.append(tag)  # nested track start (rare but possible)
                else:
                    inside_chunk = True
                    current_chunk = []
                current_chunk.append(line)
                continue

            if inside_chunk:
                current_chunk.append(line)
                # Detect any nested opening tags inside track
                if stripped.startswith("<") and not stripped.startswith("</"):
                    stack.append(tag)
                # Detect closing of a block
                if stripped == ">":
                    if stack:
                        stack.pop()
                    else:
                        inside_chunk = False
                        chunks.append("".join(current_chunk))
                        current_chunk = []

        return chunks

    @staticmethod
    def slice_item(item, start_sec, end_sec):
        pos_match = re.search(r'POSITION ([\d\.]+)', item)
        length_match = re.search(r'LENGTH ([\d\.]+)', item)
        soffs_match = re.search(r'SOFFS ([\d\.]+)', item)
        midi_match = re.search(r'<SOURCE MIDI', item)

        if midi_match:
            return ''  # Remove MIDI items entirely

        if pos_match and length_match and soffs_match:
            new_item = ''
            pos = float(pos_match.group(1))
            length = float(length_match.group(1))
            soffs = float(soffs_match.group(1))
            item_end = pos + length

            # Keep only items fully or partially within the slice
            if item_end > start_sec and pos < end_sec:
                new_pos = max(pos, start_sec) - start_sec
        
                new_soffs = soffs + max(0, start_sec - pos)

                if "A.wav" in item:
                    print(start_sec, end_sec, soffs, pos, soffs, new_soffs)
                
                new_end = min(item_end, end_sec)
                new_length = new_end - max(pos, start_sec)
                new_item = re.sub(r'POSITION ([\d\.]+)', f'POSITION {new_pos}', item)
                new_item = re.sub(r'LENGTH ([\d\.]+)', f'LENGTH {new_length}', new_item)
                new_item = re.sub(r'SOFFS ([\d\.]+)', f'SOFFS {new_soffs}', new_item)
                return new_item
        return ''

    
    @staticmethod
    def trim_rpp_by_markers(rpp_file_path: str, output_dir: str, markers: list):
        output_files = []
        rpp_file_parent = os.path.dirname(rpp_file_path)

        for marker in markers:
            start_seconds = marker["start_seconds"]
            end_seconds = marker["end_seconds"]

            with open(rpp_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            items = RppUtils.find_chunks(lines, tag="<ITEM")

            with open(rpp_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            for item in items:
                new_item = RppUtils.slice_item(item, start_seconds, end_seconds)
                content = content.replace(item, new_item)

            # pattern = r"^\s*MARKER\s+\d+\s+\d+(?:\.\d+)?\s+(\S+)"
            pattern = r"^(\s*MARKER)\s+\d+\s+\d+(?:\.\d+)?\s+(\S+)(.*)$"
            content = re.sub(
                pattern,
                lambda match: f"{match.group(1)} 1 0 {match.group(2)}" if match.group(2) == marker["name"] else "",
                content,
                flags=re.MULTILINE
            )
                
            output_folderpath = os.path.join(output_dir, marker["name"])
            while os.path.exists(output_folderpath):
                output_folderpath = output_folderpath + "_"
            os.makedirs(output_folderpath, exist_ok=True)

            output_filepath = os.path.join(output_folderpath, f"trimmed_rpp_{marker['name']}")
            while os.path.exists(output_filepath + ".rpp"):
                output_filepath = output_filepath + "_"
            output_filepath = output_filepath + ".rpp"
            with open(output_filepath, 'w') as file:
                file.write(content)

            if "Media" in os.listdir(rpp_file_parent):
                media_src = os.path.join(rpp_file_parent, "Media")
                media_dst = os.path.join(output_folderpath, "Media")
                shutil.copytree(media_src, media_dst, dirs_exist_ok=True)
            
            marker["cut_rpp_filepath"] = output_folderpath
            output_files.append(marker.copy())

        return output_files
    
    
    @staticmethod
    def trim_audio_by_markers(audio_file_path: str, audio_vocal_file_path: str, output_dir: str, markers: list):
        output_files = []
        for marker in markers:
            start_seconds = marker["start_seconds"]
            end_seconds = marker["end_seconds"]
            
            audio, sr = librosa.load(audio_file_path, sr=None, mono=False)
            audio_vocal, sr = librosa.load(audio_vocal_file_path, sr=None, mono=False)

            # Calculate start and end sample indices
            start_sample = int(start_seconds * sr)
            end_sample = int(end_seconds * sr)

            # Extract the section
            audio_section = audio[..., start_sample:end_sample]
            audio_vocal_section = audio_vocal[..., start_sample:end_sample]

            output_filepath = os.path.join(output_dir, f"trimmed_audio_{marker['name']}")
            while os.path.exists(output_filepath + ".mp3"):
                output_filepath = output_filepath + "_"
            output_filepath = output_filepath + ".mp3"
            output_vocal_filepath = os.path.join(output_dir, f"trimmed_audio_vocal_{marker['name']}")
            while os.path.exists(output_vocal_filepath + ".mp3"):
                output_vocal_filepath = output_vocal_filepath + "_"
            output_vocal_filepath = output_vocal_filepath + ".mp3"
            sf.write(output_filepath, audio_section.T, sr)
            sf.write(output_vocal_filepath, audio_vocal_section.T, sr)
            marker["cut_audio_filepath"] = output_filepath
            marker["cut_audio_with_vocals_filepath"] = output_vocal_filepath
            output_files.append(marker.copy())

        return output_files
                    
