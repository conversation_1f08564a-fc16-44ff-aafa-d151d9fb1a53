import mimetypes
import time
from typing import Any
import boto3

from app.env import env

styles_bucket = "song-styles"


s3 = boto3.client(
    "s3",
    aws_access_key_id=env.AWS_ACCESS_KEY,
    aws_secret_access_key=env.AWS_SECRET_KEY,
    region_name=env.AWS_REGION,
)

class AWS_S3:
    @staticmethod
    def download_file(bucket_name: str, key: str, file_path: Any):
        try:
            s3.download_file(bucket_name, key, file_path)
            return file_path
        except Exception as e:
            raise Exception("AWS_S3.download_file: " + str(e)) from e

    @staticmethod
    def put_signed_url(bucket_name: str, key: str, contentType: str):
        try:
            response = s3.generate_presigned_url(
                "put_object",
                Params={"Bucket": bucket_name, "Key": key, "ContentType": contentType},
                ExpiresIn=3600,
                HttpMethod="PUT",
            )
            return response
        except Exception as e:
            raise Exception(f"AWS_S3.put_signed_url: {str(e)}") from e

    @staticmethod
    def copy_object(
        source_bucket: str, source_key: str, dest_bucket: str, dest_key: str
    ):
        try:
            copy_source = {"Bucket": source_bucket, "Key": source_key}
            s3.copy_object(CopySource=copy_source, Bucket=dest_bucket, Key=dest_key)
            return True
        except Exception as e:
            raise Exception(f"AWS_S3.copy_object: {str(e)}") from e

    @staticmethod
    def get_signed_url(path: str, ttl:int = 3600):
        try:
            bucket_name, key = S3Utils.get_bucket_key_from_s3_path(path)
            ext = key.split(".")[::-1][0]
            f = int(time.time())
            
            response = s3.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": bucket_name,
                    "Key": key,
                    "ResponseContentDisposition": f'attachment; filename="{f}.{ext}"',
                },
                ExpiresIn=ttl,
            )
            return response
        except Exception as e:
            raise Exception(f"AWS_S3.get_signed_url: {str(e)}") from e

    @staticmethod
    def upload_file(filepath: Any, bucket_name: str, key: str):
        try:
            content_type, _ = mimetypes.guess_type(filepath)
            if content_type is None:
                content_type = "application/octet-stream"
            s3.upload_file(
                filepath, bucket_name, key, ExtraArgs={"ContentType": content_type}
            )
        except Exception as e:
            raise Exception(f"AWS_S3.upload_file: {str(e)}") from e

    @staticmethod
    def delete_file(bucket_name: str, key: str):
        try:
            s3.delete_object(Bucket=bucket_name, Key=key)
        except Exception as e:
            raise Exception(f"AWS_S3.delete_file: {str(e)}") from e


class S3Utils:
   
    @staticmethod
    def get_bucket_key_from_s3_path(s3_path: str) -> tuple[str, str]:
        """Extract bucket name and key from an S3 path like 'my-bucket/path/to/file.txt'."""
        # Remove leading slash if present
        if s3_path.startswith('/'):
            s3_path = s3_path[1:]

        first_slash_index = s3_path.find('/')
        if first_slash_index == -1:
            raise ValueError("Invalid S3 path format")

        bucket = s3_path[:first_slash_index]
        key = s3_path[first_slash_index + 1:]

        return  bucket, key