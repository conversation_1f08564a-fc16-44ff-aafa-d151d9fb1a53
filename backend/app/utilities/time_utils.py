import time
from datetime import datetime

class TimeUtils:
    
    @staticmethod
    def timestamp_mills():
        return int(time.time_ns() / 1000000)
    
    
    @staticmethod
    def datetime_str():
        return datetime.now().strftime("%Y%m%d_%H%M%S")


    @staticmethod
    def bar_beat_to_tick(bar_beat, time_sig, ticks_per_beat):
        bar = int(bar_beat.split(".")[0])
        beat = int(bar_beat.split(".")[1])
        tick = ticks_per_beat * time_sig * (bar - 1) + ticks_per_beat * (beat - 1)
        return tick
    
    
    @staticmethod
    def get_bar_beat(cr_time, ticks_per_beat, numerator):
        """
        Returns the bar and beat (1-based) for a given absolute cr_time in ticks.
        """
        absolute_beats = cr_time / ticks_per_beat
        bar = int(absolute_beats // numerator) + 1
        beat = int(absolute_beats % numerator) + 1
        return bar, beat


    @staticmethod
    def bar_beat_to_seconds(bar, beat, bpm, beats_per_measure):
        total_beats = (bar - 1) * beats_per_measure + (beat - 1)
        seconds_per_beat = 60 / float(bpm)
        return total_beats * seconds_per_beat



    
    