from typing import Dict, List, Callable, Any
from fastapi import WebSocket

class WebSocketManager:
    def __init__(self):
        # Store active connections with client_id as key
        self.active_connections: Dict[str, WebSocket] = {}
        # Store message handlers
        self.message_handlers: List[Callable] = []
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Connect a new client and store the connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        await self.send_personal_message({"type": "connection_established"}, client_id)
    
    def disconnect(self, client_id: str):
        """Remove a client connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
    
    async def send_personal_message(self, message: Any, client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            if isinstance(message, dict) or isinstance(message, list):
                await self.active_connections[client_id].send_json(message)
            else:
                await self.active_connections[client_id].send_text(str(message))
    
    async def broadcast(self, message: Any):
        """Send a message to all connected clients"""
        for client_id in list(self.active_connections.keys()):
            try:
                if isinstance(message, dict) or isinstance(message, list):
                    await self.active_connections[client_id].send_json(message)
                else:
                    await self.active_connections[client_id].send_text(str(message))
            except Exception:
                # If sending fails, disconnect the client
                self.disconnect(client_id)
    
    def register_message_handler(self, handler: Callable):
        """Register a function to handle incoming messages"""
        self.message_handlers.append(handler)
    
    async def handle_message(self, message: str, client_id: str):
        """Process incoming messages from clients"""
        for handler in self.message_handlers:
            try:
                await handler(message, client_id, self)
            except Exception as e:
                print(f"Error in message handler: {e}")

# Singleton instance
ws_manager = WebSocketManager()