from typing import Optional, Any
import asyncio
from app.utilities.websocket_manager import ws_manager

async def send_status_update(client_id: str, status_type: str, message: str, data: Optional[dict] = None):
    """
    Utility function to send status updates to the client via WebSocket.
    Can be called from nested functions to provide real-time updates.
    
    Args:
        client_id: The client ID to send the update to
        status_type: Type of status update (e.g., 'processing', 'complete', 'error')
        message: Human-readable message about the status
        data: Optional additional data to include in the update
    """
    payload: Any = {
        "type": "status_update",
        "status": status_type,
        "message": message
    }
    
    if data:
        payload["data"] = data
    
    # Use asyncio.create_task to avoid blocking if called from a synchronous context
    loop = asyncio.get_event_loop()
    if loop.is_running():
        asyncio.create_task(ws_manager.send_personal_message(payload, client_id))
    else:
        await ws_manager.send_personal_message(payload, client_id)

async def send_streaming_update(client_id: str, update_type: str, content: str, data: Optional[dict] = None):
    """
    Send a streaming update to a client
    
    Args:
        client_id: The client ID to send the update to
        update_type: The type of update (content_chunk, tool_call_name, tool_call_args)
        content: The content of the update
        data: Additional data to include in the update
    """
    payload: Any = {
        "type": "streaming_update",
        "update_type": update_type,
        "content": content
    }
    
    if data:
        payload["data"] = data
    
    await ws_manager.send_personal_message(payload, client_id)

# Synchronous version for functions that can't use async/await
def send_status_update_sync(client_id: str, status_type: str, message: str, data: Optional[dict] = None):
    """
    Synchronous version of send_status_update for use in non-async functions.
    Creates a new event loop if needed.
    
    Args:
        client_id: The client ID to send the update to
        status_type: Type of status update (e.g., 'processing', 'complete', 'error')
        message: Human-readable message about the status
        data: Optional additional data to include in the update
    """
    payload: Any = {
        "type": "status_update",
        "status": status_type,
        "message": message
    }
    
    if data:
        payload["data"] = data
    
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is already running, create a task
            asyncio.create_task(ws_manager.send_personal_message(payload, client_id))
        else:
            # If loop exists but not running, run the coroutine
            loop.run_until_complete(ws_manager.send_personal_message(payload, client_id))
    except RuntimeError:
        # If no event loop exists in this thread, create a new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(ws_manager.send_personal_message(payload, client_id))