from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect
import json
import uuid

from app.utilities.websocket_manager import ws_manager
from app.ai_assistant.assistant_state import AssistantState
from app.ai_assistant.chat_completion import (
    chat_completion_function, chat_completion,
    chat_completion_function_stream, chat_completion_stream
)
from app.ai_assistant.tool_registry import build_tool_map

router = APIRouter()
assistant_state = AssistantState.get()

@router.websocket("/ws/chat/{client_id}")
async def websocket_chat_endpoint(websocket: WebSocket, client_id: str = None):
    # Generate a client ID if not provided
    if not client_id:
        client_id = str(uuid.uuid4())
    
    try:
        await ws_manager.connect(websocket, client_id)
        
        # Send initial connection confirmation
        await ws_manager.send_personal_message({
            "type": "connection_status",
            "status": "connected",
            "client_id": client_id
        }, client_id)
        
        # Main message loop
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                print(f"Received message: {client_id} : {message}")
                
                # Handle different message types
                if message.get("type") == "chat_message":
                    await handle_chat_message(message, client_id)
                elif message.get("type") == "file_upload":
                    await handle_file_upload(message, client_id)
                elif message.get("type") == "reset":
                    await handle_reset(client_id)
                
            except json.JSONDecodeError:
                await ws_manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, client_id)
    except WebSocketDisconnect:
        ws_manager.disconnect(client_id)

async def handle_chat_message(message, client_id):
    """Handle chat messages from the client"""
    try:
        # Send acknowledgment that message was received
        await ws_manager.send_personal_message({
            "type": "status",
            "status": "processing",
            "message": "Processing your request..."
        }, client_id)
        
        user_input = message.get("content", "")
        use_streaming = message.get("streaming", True)  # Default to streaming if not specified
        
        # Add user message to conversation history
        assistant_state.conversation_history.append({"role": "user", "content": user_input})
        
        # Build outbound list with fresh project summary
        outbound = assistant_state.conversation_history[:]
        outbound.insert(1, {
            "role": "system",
            "name": "project_summary",
            "content": assistant_state.project_summary
        })
        
        # Send start message to initialize streaming UI
        if use_streaming:
            await ws_manager.send_personal_message({
                "type": "streaming_start",
                "message": "Starting streaming response..."
            }, client_id)
        
        # First OpenAI call with client_id for status updates (with or without streaming)
        if use_streaming:
            assistant = await chat_completion_function_stream(outbound, client_id)
        else:
            assistant = chat_completion_function(outbound, client_id)
            
        assistant_state.conversation_history.append(assistant.to_dict())
        
        # Send intermediate response to show progress
        await ws_manager.send_personal_message({
            "type": "intermediate_response",
            "message": "Analyzing your request..."
        }, client_id)
        
        # If tool requested
        if assistant.tool_calls:
            # Build tool map with client_id for status updates
            tool_map = build_tool_map(client_id)
            for tool_call in assistant.tool_calls:
                fn_name = tool_call.function.name
                args = json.loads(tool_call.function.arguments or "{}")
                
                # Send status update about tool execution
                await ws_manager.send_personal_message({
                    "type": "tool_execution",
                    "tool": fn_name,
                    "status": "running"
                }, client_id)
                
                tool_fn = tool_map.get(fn_name)
                if tool_fn is None:
                    result = f"Error: Unknown tool '{fn_name}'"
                else:
                    try:
                        # Tool functions now handle their own status updates
                        result = tool_fn(**args)
                    except Exception as e:
                        result = f"Error while executing '{fn_name}': {e}"
                
                # Function role message
                assistant_state.conversation_history.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "name": fn_name,
                    "content": result
                })
                
                # Send tool execution result
                await ws_manager.send_personal_message({
                    "type": "tool_execution",
                    "tool": fn_name,
                    "status": "completed"
                }, client_id)
            
            # Follow-up LLM call for final response with client_id for status updates
            outbound2 = assistant_state.conversation_history[:]
            
            # Send streaming start message for the follow-up response
            if use_streaming:
                await ws_manager.send_personal_message({
                    "type": "streaming_start",
                    "message": "Generating final response..."
                }, client_id)
                follow_up = await chat_completion_stream(outbound2, client_id)
            else:
                follow_up = chat_completion(outbound2, client_id)
                
            assistant_state.conversation_history.append(follow_up.to_dict())
        
        # Send streaming end message
        if use_streaming:
            await ws_manager.send_personal_message({
                "type": "streaming_end",
                "message": "Streaming complete"
            }, client_id)
        
        # Send final response with complete conversation
        await ws_manager.send_personal_message({
            "type": "chat_response",
            "conversation": assistant_state.conversation_history
        }, client_id)
        
    except Exception as e:
        error_message = f"Error processing chat message: {str(e)}"
        print(error_message)
        assistant_state.conversation_history.append({
            "role": "assistant", 
            "content": f"I'm sorry, I couldn't process your request. Please try again \n Error: {str(e)}"
        })
        
        await ws_manager.send_personal_message({
            "type": "error",
            "message": error_message,
            "conversation": assistant_state.conversation_history
        }, client_id)

async def handle_file_upload(message, client_id):
    """Handle file upload messages from the client"""
    try:
        project_filepath = message.get("project_filepath", "")
        midi_filepath = message.get("midi_filepath", "")
        output_dir = message.get("output_dir", "")
        
        # Send status update
        await ws_manager.send_personal_message({
            "type": "status",
            "status": "loading_project",
            "message": "Loading project..."
        }, client_id)
        
        # Load the project
        assistant_state.load_project(project_filepath)
        
        # Send response with conversation
        await ws_manager.send_personal_message({
            "type": "file_upload_response",
            "conversation": assistant_state.conversation_history
        }, client_id)
        
    except Exception as e:
        error_message = f"Error loading project: {str(e)}"
        print(error_message)
        
        await ws_manager.send_personal_message({
            "type": "error",
            "message": error_message
        }, client_id)

async def handle_reset(client_id):
    """Handle reset request from the client"""
    try:
        # Reset the assistant state
        assistant_state.discard()
        
        # Send response
        await ws_manager.send_personal_message({
            "type": "reset_response",
            "conversation": assistant_state.conversation_history
        }, client_id)
        
    except Exception as e:
        error_message = f"Error resetting chat: {str(e)}"
        print(error_message)
        
        await ws_manager.send_personal_message({
            "type": "error",
            "message": error_message
        }, client_id)