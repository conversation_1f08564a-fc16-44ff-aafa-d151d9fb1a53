{"name": "style-manager-app", "version": "1.2.0", "private": true, "scripts": {"run-test": "python -m app.tests.test", "run-windows": "run_windows.bat", "win-dev": "run_windows.bat --dev", "mac-dev": "sh run_macos.sh --dev", "start-backend": "cd backend && uv run app/main.py", "dev": "next dev", "build": "next build", "start": "next start", "tauri": "tauri"}, "dependencies": {"@animesh-melodyze/melodyze-ts-ui-modules": "1.0.12", "@animesh-melodyze/ts-shared": "^1.0.9", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^6.1.8", "@mui/material": "^6.1.8", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tauri-apps/api": "^2.2.0", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-fs": "^2.2.1", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.20.1", "lucide-react": "^0.503.0", "next": "^14.2.26", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.1", "react-icons": "^5.3.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "shiki": "^3.4.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "devDependencies": {"@tauri-apps/cli": "^2.2.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.8", "eslint": "^8", "eslint-config-next": "14.2.23", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}