#!/bin/bash
set -e

# Initialize DEV_MODE to false
DEV_MODE=false

# Check if the first argument is --dev
if [ "$1" = "--dev" ]; then
  DEV_MODE=true
  echo "Running in Development Mode..."
fi

# not in dev
if [ "$DEV_MODE" = false ]; then
  echo "Updating code from Git..."
  git fetch
  git pull
fi

echo "Setting up backend..."
cd backend

echo "Starting backend server..."
uv run app/main.py &


echo "Setting up frontend..."
cd ..

# not in dev
if [ "$DEV_MODE" = false ]; then
  echo "Installing Node dependencies..."
  npm install
fi

echo "Starting development server..."
npm run tauri dev