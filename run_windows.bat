@echo off
SETLOCAL

set "DEV_MODE=false"

if "%1"=="--dev" (
    set "DEV_MODE=true"
    echo Running in Development Mode...
)

if "%DEV_MODE%"=="false" (
    echo Updating code from Git...
    git fetch
    git pull
)

echo Setting up backend...
cd backend

REM Start backend in background
start /B "Backend Server" cmd /c "uv run app/main.py"

cd ..

echo Setting up frontend...

if "%DEV_MODE%"=="false" (
    echo Installing Node dependencies...
    call npm install
)

echo About to start Tauri dev server...
call npm run tauri dev
