@echo off

:: Check for Python
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Python...
    choco install python --version=3.11.0 -y
)

:: Check for Node.js
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Node.js...
    choco install nodejs -y
)

:: Check for Rust
where cargo >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Rust...
    curl --proto "=https" --tlsv1.2 -sSf https://sh.rustup.rs | sh
    setx PATH "%USERPROFILE%\.cargo\bin;%PATH%"
)

:: Check for Tauri CLI
where tauri >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing Tauri CLI...
    npm install -g @tauri-apps/cli
)

