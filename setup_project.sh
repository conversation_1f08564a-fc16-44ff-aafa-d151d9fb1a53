#!/bin/bash

# Check for required tools and install missing ones
# install_dependencies() {
  # Check for Python 3
if ! command -v python3 &> /dev/null; then
echo "Installing Python..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    brew install python@3.11
else
    choco install python --version=3.11.0
fi
fi

# Check for Node.js
if ! command -v npm &> /dev/null; then
echo "Installing Node.js..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    brew install node
else
    choco install nodejs
fi
fi

# Check for Rust
if ! command -v cargo &> /dev/null; then
echo "Installing Rust..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env
fi

# Check for Tauri CLI
if ! command -v tauri &> /dev/null; then
echo "Installing Tauri CLI..."
npm install -g @tauri-apps/cli
fi
# }