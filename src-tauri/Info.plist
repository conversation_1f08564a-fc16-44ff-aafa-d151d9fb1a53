<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Basic App Info -->
    <key>CFBundleDevelopmentRegion</key>
    <string>English</string>
    <key>CFBundleDisplayName</key>
    <string><PERSON><PERSON> Reaper Assistant</string>
    <key>CFBundleExecutable</key>
    <string>app</string>
    <key>CFBundleIconFile</key>
    <string>icon.icns</string>
    <key>CFBundleIdentifier</key>
    <string>ai.melodyze.style</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string><PERSON><PERSON> Reaper Assistant</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>0.1.0</string>
    <key>CFBundleVersion</key>
    <string>20240315.120000</string> <!-- Updated date format -->

    <!-- Network Security -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
        <key>NSAllowsLocalNetworking</key>
        <true/>
    </dict>

    <!-- Required Permissions -->
    <key>NSCameraUsageDescription</key>
    <string>Required for style analysis features</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>Required for audio input features</string>
    
    <!-- Sandbox Entitlements -->
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>

    <!-- System Requirements -->
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string> <!-- Catalina or newer -->
    <key>LSRequiresCarbon</key>
    <false/> <!-- Modern apps should use false -->
    <key>NSHighResolutionCapable</key>
    <true/>
    
    <!-- UI Configuration -->
    <key>NSRequiresAquaSystemAppearance</key>
    <false/>
    <key>LSUIElement</key>
    <false/>

    <!-- Privacy Controls -->
    <key>NSAppleEventsUsageDescription</key>
    <string>This app needs permission to communicate with system services</string>
    <key>NSLocationUsageDescription</key>
    <string></string>
    <key>NSContactsUsageDescription</key>
    <string></string>
    <key>NSCalendarsUsageDescription</key>
    <string></string>
</dict>
</plist>