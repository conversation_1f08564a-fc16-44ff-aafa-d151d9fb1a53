#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use reqwest;
use std::env;
use std::path::PathBuf;
use std::process::{Child, Command, Stdio};
use std::sync::{<PERSON>, Mutex};
use tokio::runtime::Runtime;
use tokio::signal;

#[derive(Default)]
struct AppState {
    backend_process: Mutex<Option<Child>>,
}

fn get_backend_paths() -> Result<(PathBuf, PathBuf), String> {
    // Get the current executable's directory
    let exe_dir = env::current_exe()
        .map_err(|e| format!("Failed to get executable path: {}", e))?
        .parent()
        .ok_or("Failed to get executable directory")?
        .to_path_buf();

    // Possible paths for backend executable
    let possible_paths = vec![
        // Development environment paths
        exe_dir.join("../backend/dist/fastapi_app"),
        exe_dir.join("../backend/dist/fastapi_app.exe"),
        
        // Production environment paths (Windows)
        PathBuf::from("C:\\Program Files\\Melodyze Reaper Assistant\\_up_\\backend\\dist\\fastapi_app.exe"),
        
        // Production environment paths (macOS)
        PathBuf::from("/Applications/Melodyze Reaper Assistant.app/Contents/Resources/_up_/backend/dist/fastapi_app"),
    ];

    // Find the first existing path
    for path in possible_paths {
        if path.exists() {
            // Get the directory of the backend executable
            let backend_dir = path
                .parent()
                .ok_or("Failed to get backend directory")?
                .to_path_buf();

            return Ok((path, backend_dir));
        }
    }

    Err("Backend executable not found in any of the expected locations".to_string())
}

#[tauri::command]
fn start_fastapi(state: tauri::State<Arc<AppState>>) -> Result<String, String> {
    // Get backend paths
    let (backend_path, backend_dir) = get_backend_paths()?;

    let child = Command::new(&backend_path)
        .current_dir(&backend_dir)
        .stdout(Stdio::inherit())
        .stderr(Stdio::inherit())
        .spawn()
        .map_err(|e| format!("Failed to start backend: {}", e))?;

    let mut process_guard = state.backend_process.lock().unwrap();
    *process_guard = Some(child);

    Ok("Backend started.".to_string())
}

fn stop_fastapi(state: Arc<AppState>) {
    let rt = Runtime::new().expect("Failed to create Tokio runtime");

    let shutdown_url = "http://127.0.0.1:8009/shutdown"; // Update with actual FastAPI shutdown URL

    let _result = rt.block_on(async {
        match reqwest::get(shutdown_url).await {
            Ok(response) if response.status().is_success() => {
                println!("FastAPI server shutdown request successful.");
            }
            Ok(response) => {
                println!("FastAPI shutdown request failed: {:?}", response.status());
            }
            Err(e) => {
                println!("Failed to send shutdown request: {:?}", e);
            }
        }
    });

    let mut process_guard = state.backend_process.lock().unwrap();
    if let Some(mut child) = process_guard.take() {
        if let Err(e) = child.kill() {
            println!("Failed to kill backend process: {}", e);
        } else {
            println!("Backend process killed.");
        }
    }
}

fn main() {
    let app_state = Arc::new(AppState::default());

    let rt = Runtime::new().expect("Failed to create Tokio runtime");

    let app_state_clone = app_state.clone();
    rt.spawn(async move {
        signal::ctrl_c().await.expect("Failed to listen for ctrl_c");
        stop_fastapi(app_state_clone);
    });

    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .manage(app_state.clone())
        .invoke_handler(tauri::generate_handler![start_fastapi])
        .on_window_event({
            let app_state_clone = app_state.clone();
            move |_window, event| {
                if let tauri::WindowEvent::CloseRequested { .. } = event {
                    stop_fastapi(app_state_clone.clone());
                }
            }
        })
        .run(tauri::generate_context!())
        .expect("Error while running Tauri application");
}

// #![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// use std::process::{Command, Child, Stdio};
// use std::path::Path;
// use std::sync::{Arc, Mutex};
// use tokio::runtime::Runtime;
// use tokio::signal;

// #[derive(Default)]
// struct AppState {
//     backend_process: Mutex<Option<Child>>,
// }

// #[tauri::command]
// fn start_fastapi(state: tauri::State<Arc<AppState>>) -> Result<String, String> {
//     let backend_path = if cfg!(target_os = "windows") {
//         "C:\\Program Files\\Melodyze Reaper Assistant\\_up_\\backend\\dist\\fastapi_app.exe"
//     } else {
//         "/Applications/Melodyze Reaper Assistant.app/Contents/Resources/_up_/backend/dist/fastapi_app"
//     };

//     if !Path::new(backend_path).exists() {
//         return Err(format!("Backend not found at path: {}", backend_path));
//     };

//     let backend_dir = if cfg!(target_os = "windows") {
//         "C:\\Program Files\\Melodyze Reaper Assistant\\_up_\\backend\\dist"
//     } else {
//         "/Applications/Melodyze Reaper Assistant.app/Contents/Resources/_up_/backend/dist"
//     };

//     let child = Command::new(backend_path)
//         .current_dir(backend_dir)
//         .stdout(Stdio::inherit())
//         .stderr(Stdio::inherit())
//         .spawn()
//         .map_err(|e| format!("Failed to start backend: {}", e))?;

//     let mut process_guard = state.backend_process.lock().unwrap();
//     *process_guard = Some(child);

//     Ok("Backend started.".to_string())
// }

// fn stop_fastapi(state: Arc<AppState>) {
//     let mut process_guard = state.backend_process.lock().unwrap();
//     if let Some(mut child) = process_guard.take() {
//         if let Err(e) = child.kill() {
//             println!("Failed to kill backend process: {}", e);
//         } else {
//             println!("Backend process killed.");
//         }
//     }
// }

// fn main() {
//     let app_state = Arc::new(AppState::default());

//     let rt = Runtime::new().expect("Failed to create Tokio runtime");

//     let app_state_clone = app_state.clone();
//     rt.spawn(async move {
//         signal::ctrl_c().await.expect("Failed to listen for ctrl_c");
//         stop_fastapi(app_state_clone);
//     });

//     tauri::Builder::default()
//         .manage(app_state.clone()) // Correctly pass app state
//         .invoke_handler(tauri::generate_handler![start_fastapi])
//         .on_window_event({
//             let app_state_clone = app_state.clone();
//             move |_window, event| {
//                 if let tauri::WindowEvent::CloseRequested { .. } = event {
//                     stop_fastapi(app_state_clone.clone());
//                 }
//             }
//         })
//         .run(tauri::generate_context!())
//         .expect("Error while running Tauri application");
// }
