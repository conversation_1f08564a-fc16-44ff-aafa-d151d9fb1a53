{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "<PERSON><PERSON> Assistant", "version": "0.1.0", "identifier": "ai.melodyze.rpr_assistant", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "<PERSON><PERSON> Assistant", "width": 950, "height": 650, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "resources": [], "icon": ["icons/melo_32x32.png", "icons/melo_128x128.png", "icons/melo_256x256.png", "icons/melo.icns", "icons/melo.ico"]}}