@import url("https://fonts.googleapis.com/css2?family=Iceland:wght@400&family=Inter:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 0 0% 93%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 93%;

    --popover: 0 0% 6%;
    --popover-foreground: 0 0% 93%;

    --primary: 326 100% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 180 100% 50%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 180 100% 50%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 326 100% 60%;

    --radius: 0.5rem;

    --chart-1: 326 100% 60%;
    --chart-2: 180 100% 50%;
    --chart-3: 39 100% 50%;
    --chart-4: 262 100% 50%;
    --chart-5: 120 100% 50%;

    /* Company Theme Variables */
    --melodyze-primary: 241 56 241;
    --melodyze-secondary: 8 12 40;
    --melodyze-tertiary: 3 3 22;
    --melodyze-accent-cyan: 34 211 238;
    --melodyze-accent-purple: 147 51 234;
    --melodyze-accent-pink: 236 72 153;
    --melodyze-accent-indigo: 99 102 241;

    /* Sidebar specific gradients */
    --sidebar-gradient: linear-gradient(180deg, rgba(241, 56, 241, 0.12) 0%, rgba(8, 12, 40, 0.15) 60%, rgba(3, 3, 22, 0.8) 100%);
    --sidebar-hover: rgba(241, 56, 241, 0.08);
    --sidebar-active: rgba(34, 211, 238, 0.15);
  }
}

body {
  @apply bg-background text-foreground;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.tooltip {
  overflow-y: auto; /* Allows vertical scrolling */
  white-space: pre-wrap; /* Allows text to wrap to the next line */
  padding: 0.5rem; /* Optional: padding for better spacing */
}

/* Hide the native checkbox visually, but keep it accessible */
.sr-only {
  position: absolute !important;
  height: 1px;
  width: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* The toggle container */
.toggle-switch {
  position: relative;
  width: 44px; /* ~w-11 in Tailwind */
  height: 24px; /* ~h-6 in Tailwind */
  background-color: #e5e7eb; /* gray-200 */
  border-radius: 9999px; /* fully rounded */
  transition: background-color 0.2s ease-in-out;
}

input[type="checkbox"]:checked ~ .toggle-switch {
  background-color: #2563eb;
}

/* The circle/knob */
.toggle-switch::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: #545050;
  border: 1px solid #545050;
  border-radius: 9999px;
  transition: transform 0.2s ease-in-out;
}

/* Shift the circle to the right when checked */
input[type="checkbox"]:checked ~ .toggle-switch::after {
  transform: translateX(20px);
  border: 1px solid #fff;
}

.app-container {
  text-align: center;
  padding: 20px;
}

@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
