"use client";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import StyleTransferOld from "../pages/style/transfer-old";
import StyleUpload from "../pages/style/Upload";
import StyleUploadOld from "../pages/style/upload-old";
import StyleManagement from "../pages/style/Management";
import StyleAPIService from "@/utils/styleApiService";
import Sidebar, { ViewType } from "@/components/sidebar";
import TwelveScaleRender from "@/pages/render/twelveScaleRender";
import AIAssistantChat from "../pages/chat/AIAssistantChat";
import AppLoader from "@/components/screens/AppLoader";
import StyleTransferPage from "@/pages/style/transfer";

export default function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const page = searchParams?.get("page") as ViewType;

  const [loading, setLoading] = useState(true);
  const [useRedesign, setUseRedesign] = useState(true);

  const navigateTo = (target: ViewType) => {
    router.push(`/?page=${target}`);
  };

  const handleBackPress = () => {
    router.push("/");
  };

  const handleOpenReaper = () => {
    StyleAPIService.startReaper().then();
  };

  const handleLogout = () => {
    console.log("Logging out...");
  };

  const toggleDesign = () => {
    setUseRedesign(!useRedesign);
  };

  useEffect(() => {
    const initializeApp = async () => {
      setLoading(true);
      try {
        let tries = 0;
        const checkBackend = setInterval(async () => {
          try {
            const res = await fetch("http://127.0.0.1:8009");
            if (res.ok) {
              clearInterval(checkBackend);
              setLoading(false);
              console.log("Backend is up and running!");
            }
          } catch (err) {
            console.log(`Waiting for backend... (${++tries})`);
          }
        }, 1000);
      } catch (error: any) {
        console.error("App initialization failed:", error);
        const res = await fetch("http://127.0.0.1:8009");
        if (res.ok) {
          setLoading(false);
        }
      }
    };

    initializeApp();
  }, []);

  // useEffect(() => {
  //   let intervalId: NodeJS.Timeout | null = null;
  //   if (loading) {
  //     let dotCount = 0;
  //     intervalId = setInterval(() => {
  //       dotCount = (dotCount + 1) % 4;
  //       setLoadingText(`Initializing${".".repeat(dotCount)}`);
  //     }, 500);
  //   }

  //   return () => {
  //     if (intervalId) {
  //       clearInterval(intervalId);
  //     }
  //   };
  // }, [loading]);

  if (loading) {
    return <AppLoader  />;
  }

  const renderContent = () => {
    if (useRedesign) {
      // Render redesigned components
      if (page === "upload") return <StyleUpload />;
      if (page === "transfer") return <StyleTransferPage />;
      if (page === "management") return <StyleManagement />;
    } else {
      // Render original components
      if (page === "upload") return <StyleUploadOld />;
      if (page === "transfer") return <StyleTransferOld />;
      if (page === "management") return <StyleManagement />;
    }

    if (page === "twelve-scale-render") return <TwelveScaleRender />;
    return <AIAssistantChat />;
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(180deg, rgba(8, 12, 40, 0.23) 10%, rgba(0, 0, 0, 0.23) 70%, rgba(25, 178, 194, 0.23) 100%)",
      }}
      className="flex"
    >
      <Sidebar currentPage={page} onNavigate={navigateTo} onOpenReaper={handleOpenReaper} onLogoClick={() => router.push("/")} />
      <div className="flex-1 h-screen overflow-y-auto">{renderContent()}</div>
    </div>
  );
}
