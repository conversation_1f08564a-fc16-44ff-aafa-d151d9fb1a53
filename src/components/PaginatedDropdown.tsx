import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, ChevronDown, RotateCcw } from "lucide-react";
import { createPortal } from "react-dom";

/** API response shape */
export interface FetchDataResult {
  data: string[];
  hasMore: boolean;
}

export interface PaginatedDropdownProps {
  /** Called to fetch items for a given page. Must return a Promise of shape { data, hasMore }. */
  fetchData: (page: number, pageSize: number) => Promise<FetchDataResult>;

  /**
   * Fires when user selects (or deselects) an item.
   * item === string => newly selected
   * item === null   => deselected
   */
  onSelect?: (item: string | null) => void;

  /**
   * Controlled value from parent.
   * If provided, PaginatedDropdown will display this as selected.
   * If null, it means no item is selected.
   */
  value?: string | null;

  /**
   * How many items to load per API call.
   */
  pageSize?: number;

  /**
   * Placeholder text for the display button.
   */
  placeholder?: string;

  /**
   * Placeholder for the local search input.
   */
  searchPlaceholder?: string;

  /**
   * Shown if no search results.
   */
  noResultsText?: string;

  /**
   * Extra class for styling container
   */
  className?: string;
}

const PaginatedDropdown: React.FC<PaginatedDropdownProps> = ({
  fetchData,
  onSelect,
  value,
  pageSize = 10,
  placeholder = "Select an item",
  searchPlaceholder = "Search...",
  noResultsText = "No results found",
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number; width: number } | null>(null);

  /**
   * If no `value` is provided from parent, we keep an internal state.
   * If `value` is provided, we derive from that.
   */
  const [internalSelectedItem, setInternalSelectedItem] = useState<string | null>(null);

  const dropdownRef = useRef<HTMLDivElement>(null);

  // This is our actual "selected item" in UI
  const selectedItem = value !== undefined ? value : internalSelectedItem;

  // Close dropdown when clicking outside and handle scroll
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setDropdownPosition(null);
      }
    };

    const handleScroll = () => {
      if (isOpen && dropdownRef.current) {
        const rect = dropdownRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + window.scrollY + 8,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      window.addEventListener("scroll", handleScroll);
      window.addEventListener("resize", handleScroll);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, [isOpen]);

  // ----------------------------------
  // Toggling the dropdown -> fetch first page if opening
  // ----------------------------------
  const handleToggleDropdown = async () => {
    if (isOpen) {
      setIsOpen(false);
      setDropdownPosition(null);
      return;
    }

    // Calculate position for portal
    if (dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 8,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }

    setIsOpen(true);
    setItems([]);
    setPage(1);
    setHasMore(false);
    setSearchTerm("");
    setLoading(true);

    try {
      const result = await fetchData(1, pageSize);
      setItems(result.data.map(String)); // Convert all items to string
      setHasMore(result.hasMore);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  // ----------------------------------
  // Pagination load more
  // ----------------------------------
  const handleLoadMore = async () => {
    setLoading(true);
    const nextPage = page + 1;
    try {
      const result = await fetchData(nextPage, pageSize);
      setItems((prev) => [...prev, ...result.data.map(String)]); // Convert all items to string
      setHasMore(result.hasMore);
      setPage(nextPage);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  // ----------------------------------
  // Filtered list based on searchTerm
  // ----------------------------------
  const filteredItems = items.filter((item) => item.toLowerCase().includes(searchTerm.toLowerCase()));

  // ----------------------------------
  // When user selects an item
  // ----------------------------------
  const handleItemSelect = (item: string) => {
    // If no `value` from parent, we store locally
    if (value === undefined) {
      setInternalSelectedItem(item);
    }
    // Fire parent's callback
    onSelect?.(item);
    // Close
    setIsOpen(false);
  };

  // ----------------------------------
  // Deselect
  // ----------------------------------
  const handleDeselect = () => {
    if (value === undefined) {
      setInternalSelectedItem(null);
    }
    onSelect?.(null);
    setIsOpen(false);
  };

  // ----------------------------------
  // Close dropdown if clicked outside
  // ----------------------------------
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (value !== undefined) {
      setInternalSelectedItem(value);
    } else {
      setInternalSelectedItem(null);
    }
  }, [value]);

  // ----------------------------------
  // RENDER
  // ----------------------------------
  return (
    <div ref={dropdownRef} className={`relative inline-block w-full font-inter ${className}`}>
      {/* The Toggle Button */}
      <motion.button
        type="button"
        onClick={handleToggleDropdown}
        className="
          w-full
          flex
          items-center
          justify-between
          text-white
          px-4 py-3
          rounded-xl
          shadow-lg
          transition-all duration-300
          backdrop-blur-xl
          bg-melodyze-secondary/60
          border-2 border-melodyze-cyan/20
          hover:border-melodyze-cyan/40
          hover:bg-melodyze-secondary/80
        "
        whileHover={{ scale: 1.01, y: -1 }}
        whileTap={{ scale: 0.99 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <span className={selectedItem ? "text-melodyze-cyan font-medium" : "text-gray-300/90 italic"}>{selectedItem || placeholder}</span>
        <motion.div animate={{ rotate: isOpen ? 180 : 0 }} transition={{ duration: 0.3 }}>
          <ChevronDown className="ml-2 w-5 h-5 text-melodyze-cyan" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isOpen &&
          dropdownPosition &&
          createPortal(
            <motion.div
              className="
              fixed
              border-2 border-melodyze-cyan/20
              rounded-2xl
              shadow-2xl
              z-[999999]
              bg-melodyze-secondary/95
              backdrop-blur-xl
              text-gray-200
              overflow-hidden
              min-w-fit
            "
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              style={{
                top: dropdownPosition.top,
                left: dropdownPosition.left,
                width: dropdownPosition.width,
                boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(34, 211, 238, 0.1)",
              }}
            >
              {loading && (
                <motion.div
                  className="p-6 text-center text-melodyze-cyan"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.span
                    className="inline-block mr-3 text-2xl"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    ⏳
                  </motion.span>
                  Loading...
                </motion.div>
              )}

              {!loading && (
                <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3, delay: 0.1 }}>
                  {/* Local Search */}
                  <div className="p-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-melodyze-cyan/60" />
                      <motion.input
                        type="text"
                        placeholder={searchPlaceholder}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="
                      w-full
                      bg-melodyze-tertiary/50
                      border-2 border-melodyze-cyan/20
                      rounded-xl
                      pl-10 pr-4 py-3
                      text-sm
                      text-gray-200
                      placeholder-gray-500
                      focus:outline-none
                      focus:border-melodyze-cyan/50
                      transition-all duration-300
                    "
                        whileFocus={{ scale: 1.01 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      />
                    </div>
                  </div>

                  {/* Deselect option if there's a selected item */}
                  <AnimatePresence>
                    {selectedItem && (
                      <motion.button
                        type="button"
                        onClick={handleDeselect}
                        className="
                      flex
                      items-center
                      w-full
                      text-left
                      px-4 py-3
                      bg-melodyze-tertiary/30
                      hover:bg-melodyze-tertiary/50
                      border-b border-melodyze-cyan/20
                      text-sm
                      text-melodyze-pink
                      transition-all duration-300
                    "
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        whileHover={{ x: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        Reset Selected Item <RotateCcw className="ml-2 w-4 h-4" />
                      </motion.button>
                    )}
                  </AnimatePresence>

                  <ul className="max-h-60 overflow-y-auto">
                    {filteredItems.length > 0 ? (
                      filteredItems.map((item, idx) => (
                        <li
                          key={idx}
                          onClick={() => handleItemSelect(item)}
                          className="
                        px-3 py-2
                        hover:bg-gray-100
                        cursor-pointer
                        dark:hover:bg-gray-700
                      "
                        >
                          {item}
                        </li>
                      ))
                    ) : (
                      <li className="px-3 py-2 text-gray-500 dark:text-gray-400">{noResultsText}</li>
                    )}
                  </ul>

                  {/* Load More if more data is available & there's at least 1 item */}
                  {hasMore && filteredItems.length > 0 && (
                    <button
                      type="button"
                      onClick={handleLoadMore}
                      className="
                    w-full
                    text-center
                    bg-gray-100
                    hover:bg-gray-200
                    py-2
                    text-sm
                    font-medium
                    dark:bg-gray-700
                    dark:hover:bg-gray-600
                  "
                    >
                      Load More
                    </button>
                  )}
                </motion.div>
              )}
            </motion.div>,
            document.body
          )}
      </AnimatePresence>
    </div>
  );
};

export default PaginatedDropdown;
