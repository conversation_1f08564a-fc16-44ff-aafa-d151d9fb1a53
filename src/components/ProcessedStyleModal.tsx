"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Music,
  Clock,
  User,
  Tag,
  Volume2,
  Loader2,
  Database,
  GaugeCircle,
  KeyRoundIcon,
  SectionIcon,
  Calendar
} from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import AudioPlayer from "@/components/audio-player/AudioPlayer";
import { StyleBean } from "@/models/Style";
import StyleAPIService from "@/utils/styleApiService";
import { VscSymbolRuler } from "react-icons/vsc";

interface ProcessedStyleModalProps {
  isOpen: boolean;
  onClose: () => void;
  originalStyle: StyleBean | null;
}

const ProcessedStyleModal: React.FC<ProcessedStyleModalProps> = ({
  isOpen,
  onClose,
  originalStyle,
}) => {
  const [processedStyles, setProcessedStyles] = useState<StyleBean[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch processed styles when modal opens
  useEffect(() => {
    if (isOpen && originalStyle) {
      fetchProcessedStyles();
    }
  }, [isOpen, originalStyle]);

  const fetchProcessedStyles = async () => {
    if (!originalStyle) return;

    setIsLoading(true);
    setError(null);
    try {
      const data = await StyleAPIService.getProcessedStyle(originalStyle._id);
      setProcessedStyles(data["styles"]);
    } catch (error) {
      console.error("Error fetching processed styles:", error);
      setError("Failed to load processed styles. Please try again.");
      setProcessedStyles([]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-melodyze-secondary/95 backdrop-blur-xl border border-melodyze-cyan/20 shadow-2xl max-w-6xl max-h-[90vh] overflow-hidden">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col h-full"
        >
          <DialogHeader className="flex-shrink-0 pb-4 border-b border-melodyze-cyan/20">
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center text-white font-inter font-semibold text-xl">
                <motion.div
                  whileHover={{ rotate: 180 }}
                  transition={{ duration: 0.3 }}
                >

                  <Music className="mr-3 text-melodyze-cyan h-6 w-6" />
                </motion.div>
                Processed Styles ({processedStyles.length} found)
                {originalStyle && (
                  <span className="ml-2 text-sm text-melodyze-cyan/80 font-normal">
                    {originalStyle.time_signature} | {originalStyle.genre} | {originalStyle.pitch} | {originalStyle.scale} | {originalStyle.tempo} | {originalStyle.source} | {originalStyle.annotator} | {originalStyle.swing ? "Yes" : "No"}
                  </span>
                )}
              </DialogTitle>
              <motion.button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-melodyze-tertiary/50"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-5 w-5" />
              </motion.button>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            {isLoading ? (
              <motion.div
                className="flex items-center justify-center h-64"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin text-melodyze-cyan mx-auto mb-4" />
                  <p className="text-gray-300 font-inter">Loading processed styles...</p>
                </div>
              </motion.div>
            ) : error ? (
              <motion.div
                className="flex items-center justify-center h-64"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <p className="text-red-400 font-inter mb-4">{error}</p>
                  <Button
                    onClick={fetchProcessedStyles}
                    className="bg-melodyze-cyan hover:bg-melodyze-cyan/80 text-black font-inter font-medium"
                  >
                    Try Again
                  </Button>
                </div>
              </motion.div>
            ) : (
              <div className="h-full overflow-y-auto pr-2 py-4">
                <AnimatePresence>
                  {processedStyles.length === 0 ? (
                    <motion.div
                      className="flex items-center justify-center h-64"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <p className="text-gray-400 font-inter">No processed styles found.</p>
                    </motion.div>
                  ) : (
                    <div className="space-y-4">
                      {processedStyles.map((style, index) => (
                        <motion.div
                          key={style._id}
                          initial={{ opacity: 0, y: 20, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -20, scale: 0.95 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="bg-melodyze-tertiary/60 border border-melodyze-cyan/20 rounded-xl p-5 hover:border-melodyze-cyan/40 transition-all duration-300 backdrop-blur-sm hover:shadow-lg hover:shadow-melodyze-cyan/10"
                          whileHover={{ scale: 1.01, y: -2 }}
                        >
                          {/* Style Details Grid */}
                          <motion.div
                            className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                          >
                            <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <SectionIcon className="mr-1 h-3 w-3" />
                                Section
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.section || "N/A"}</div>
                            </motion.div>

                            <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <Clock className="mr-1 h-3 w-3" />
                                Duration (Bars)
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.duration_in_bars || "N/A"}</div>
                            </motion.div>

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <Tag className="mr-1 h-3 w-3" />
                                Genre
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.genre || "N/A"}</div>
                            </motion.div> */}

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <GaugeCircle className="mr-1 h-3 w-3" />
                                Tempo
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.tempo || "N/A"}</div>
                            </motion.div> */}

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <KeyRoundIcon className="mr-1 h-3 w-3" />
                                Pitch
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.pitch || "N/A"}</div>
                            </motion.div> */}

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <VscSymbolRuler className="mr-1 h-3 w-3" />
                                Scale
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.scale || "N/A"}</div>
                            </motion.div> */}

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <User className="mr-1 h-3 w-3" />
                                Annotator
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.annotator || "N/A"}</div>
                            </motion.div> */}

                            {/* <motion.div className="space-y-1" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <Database className="mr-1 h-3 w-3" />
                                Source
                              </div>
                              <div className="text-sm text-gray-200 font-inter font-semibold">{style.source || "N/A"}</div>
                            </motion.div> */}
                          </motion.div>

                          {/* Audio Players */}
                          <motion.div
                            className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-melodyze-cyan/20"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: 0.3 }}
                          >
                            <motion.div className="space-y-2" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <Volume2 className="mr-1 h-3 w-3" />
                                Karaoke Audio
                              </div>
                              <AudioPlayer src={style.audio_path} />
                            </motion.div>

                            <motion.div className="space-y-2" whileHover={{ scale: 1.02 }}>
                              <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                                <Volume2 className="mr-1 h-3 w-3" />
                                Audio with Vocals
                              </div>
                              <AudioPlayer src={style.audio_with_vocals_path} />
                            </motion.div>
                          </motion.div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default ProcessedStyleModal;
