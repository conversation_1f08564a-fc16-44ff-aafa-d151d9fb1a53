"use client"
import React, { useRef, useState, useEffect } from "react";
import { Play, Pause, Download } from "lucide-react";
import AudioPlayerManager from "./AudioPlayerManager";
interface AudioPlayerProps {
  src: string;
  download?: boolean;
  seeker?: boolean;
  label?: string;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ 
  src, 
  download = false, 
  seeker = true,
  label
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [isPlayable, setIsPlayable] = useState(true);

  // Format time in mm:ss
  const formatTime = (time: number): string => {
    if (!isPlayable || isNaN(time) ) return "--:--";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  // Toggle play/pause
  const togglePlayPause = () => {
    if (audioRef.current && isPlayable) {
      if (isPlaying) {
        AudioPlayerManager.stop(audioRef.current);
        setIsPlaying(false);
      } else {
        AudioPlayerManager.play(audioRef.current);
        audioRef.current.play().catch(error => {
          console.error("Error playing audio:", error);
          setIsPlayable(false);
        });
        setIsPlaying(true);
      }
    }
  };

  useEffect(() => {
    const audio = audioRef.current;

    const handlePlay = () => {
      if (audio) {
        AudioPlayerManager.play(audio);
        setIsPlaying(true);
      }
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const loadMetadata = () => {
      if (audio) {
        if (isNaN(audio.duration) || audio.duration === Infinity) {
          setIsPlayable(false);
          setDuration(0);
        } else {
          setIsPlayable(true);
          setDuration(audio.duration);
        }
      }
    };

    const handleEnded = () => {
      if (audio) {
        AudioPlayerManager.stop(audio);
      }
      setIsPlaying(false);
    };

    const handleError = () => {
      console.error("Audio error encountered");
      setIsPlayable(false);
      setDuration(0);
      setIsPlaying(false);
    };

    const updateProgress = () => {
      if (audio) {
        setCurrentTime(audio.currentTime);
        const currentProgress = (audio.currentTime / audio.duration) * 100 || 0;
        setProgress(currentProgress);
      }
    };

    if (audio) {
      audio.addEventListener("play", handlePlay);
      audio.addEventListener("pause", handlePause);
      audio.addEventListener("loadedmetadata", loadMetadata);
      audio.addEventListener("ended", handleEnded);
      audio.addEventListener("timeupdate", updateProgress);
      audio.addEventListener("error", handleError);

      // Reset state when source changes
      setIsPlayable(true);
      setDuration(0);
      setCurrentTime(0);
      setProgress(0);
      
      // Attempt to load the audio
      audio.load();
    }

    return () => {
      if (audio) {
        audio.removeEventListener("play", handlePlay);
        audio.removeEventListener("pause", handlePause);
        audio.removeEventListener("loadedmetadata", loadMetadata);
        audio.removeEventListener("ended", handleEnded);
        audio.removeEventListener("timeupdate", updateProgress);
        audio.removeEventListener("error", handleError);
      }
    };
  }, [src]);

  // Handle seeker change
  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (audioRef.current && isPlayable) {
      const newTime = (Number(e.target.value) / 100) * audioRef.current.duration;
      audioRef.current.currentTime = newTime;
      setProgress(Number(e.target.value));
    }
  };

  return (
    <div className="relative w-full max-w-md mx-auto overflow-hidden audio-player-container">
      {/* Label */}
      {label && (
        <div className="text-center mb-1">
          <p className="text-xs font-medium text-cyan-300 truncate px-2">
            {label}
          </p>
        </div>
      )}
      
      <div
        className="flex flex-col px-2 py-1 rounded-lg audio-player backdrop-blur-lg bg-black/80 border border-cyan-900/40"
        style={{
          boxShadow: isHovering
            ? "0 0 15px rgba(6, 182, 212, 0.25), 0 0 3px rgba(6, 182, 212, 0.15)"
            : "0 0 10px rgba(6, 182, 212, 0.1), 0 0 2px rgba(6, 182, 212, 0.05)",
        }}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
        <div className="flex items-center space-x-2">
          {/* Play/Pause Button */}
          <button
            type="button"
            onClick={togglePlayPause}
            className={`flex items-center justify-center w-6 h-6 transition-all duration-300 rounded-full play-button bg-gradient-to-br ${
              isPlayable 
                ? "from-cyan-900 to-black hover:from-cyan-800 hover:to-black" 
                : "from-gray-700 to-gray-900 cursor-not-allowed"
            } flex-shrink-0`}
            style={{
              boxShadow: isPlaying ? "0 0 10px rgba(6, 182, 212, 0.3)" : "0 0 5px rgba(6, 182, 212, 0.1)",
              opacity: isPlayable ? 1 : 0.5
            }}
            disabled={!isPlayable}
          >
            {isPlaying ? 
              <Pause className="w-3 h-3 text-cyan-400" /> : 
              <Play className={`w-3 h-3 ml-0.5 ${isPlayable ? "text-cyan-400" : "text-gray-400"}`} />
            }
          </button>

          {/* Time and Seeker Section */}
          <div className="flex-1 flex items-center space-x-2">
            {/* Progress Bar */}
            {seeker ? (
              <div className="relative flex-1 h-6 group">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={progress}
                  onChange={handleSeek}
                  disabled={!isPlayable}
                  className={`absolute top-1/2 -translate-y-1/2 w-full h-0.5 appearance-none ${
                    isPlayable ? "cursor-pointer" : "cursor-not-allowed"
                  } outline-none rounded-lg bg-gray-700`}
                  style={{ zIndex: 10 }}
                />
                <div
                  className={`absolute top-1/2 -translate-y-1/2 h-0.5 rounded-lg pointer-events-none left-0 ${
                    isPlayable ? "bg-cyan-400" : "bg-gray-500"
                  }`}
                  style={{ width: `${progress}%`, transition: "width 0.1s ease", zIndex: 11 }}
                ></div>
              </div>
            ) : (
              <span className="text-xs font-mono text-center text-cyan-300 flex-1">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            )}

            {/* Duration */}
            {seeker && (
              <span className={`text-xs font-mono ${isPlayable ? "text-cyan-300" : "text-gray-500"} flex-shrink-0 hidden sm:inline`}>
                {formatTime(duration)}
              </span>
            )}
          </div>

          {/* Download Button */}
          {download && (
            <a
              href={src}
              download
              className={`p-1 ${isPlayable ? "text-cyan-400 hover:text-cyan-300 hover:scale-110" : "text-gray-500"} transition-transform duration-300 rounded-full flex-shrink-0`}
            >
              <Download className="w-3 h-3" />
            </a>
          )}
        </div>
      </div>

      {/* Audio Element */}
      <audio ref={audioRef} src={src}></audio>

      {/* CSS for custom range input styling */}
      <style jsx>{`
        input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 0;
          height: 0;
          background: transparent;
          cursor: pointer;
        }

        input[type="range"]::-moz-range-thumb {
          width: 0;
          height: 0;
          background: transparent;
          cursor: pointer;
          border: none;
        }

        input[type="range"]:disabled::-webkit-slider-thumb,
        input[type="range"]:disabled::-moz-range-thumb {
          cursor: not-allowed;
        }

        .audio-player-container:hover .play-button:not(:disabled) {
          transform: scale(1.05);
          transition: transform 0.3s ease;
        }

        .audio-player {
          transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
        }

        input[type="range"]::-webkit-slider-runnable-track {
          width: 100%;
          height: 2px;
          cursor: pointer;
          background: transparent;
          border-radius: 9999px;
        }

        input[type="range"]::-moz-range-track {
          width: 100%;
          height: 2px;
          cursor: pointer;
          background: transparent;
          border-radius: 9999px;
          border: none;
        }

        input[type="range"]:disabled::-webkit-slider-runnable-track,
        input[type="range"]:disabled::-moz-range-track {
          cursor: not-allowed;
        }

        /* Responsive styles */
        @media (max-width: 640px) {
          /* Keep minimal styling for mobile devices */
        }
      `}</style>
    </div>
  );
};

export default AudioPlayer;