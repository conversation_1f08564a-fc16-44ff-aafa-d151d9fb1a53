class AudioPlayerManager {
    private static instance: AudioPlayerManager;
    private currentAudio: HTMLAudioElement | null = null;

    private constructor() { }

    public static getInstance(): AudioPlayerManager {
        if (!AudioPlayerManager.instance) {
            AudioPlayerManager.instance = new AudioPlayerManager();
        }
        return AudioPlayerManager.instance;
    }

    public play(audio: HTMLAudioElement) {
        if (this.currentAudio && this.currentAudio !== audio) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
        }
        this.currentAudio = audio;
    }

    public stop(audio: HTMLAudioElement) {
        if (this.currentAudio === audio) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
            this.currentAudio = null;
        }
    }

    public pause(audio: HTMLAudioElement) {
        if (this.currentAudio === audio) {
            this.currentAudio.pause();
        }
    }
}

export default AudioPlayerManager.getInstance();