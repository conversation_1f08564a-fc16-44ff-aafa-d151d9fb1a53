import { FaArrowLeft } from "react-icons/fa";

interface BackButtonProps {
  onBackPress: () => void;
}

const BackButton: React.FC<BackButtonProps> = ({ onBackPress }) => {
  return (
    <button
      onClick={onBackPress}
      className="absolute top-4 left-4 text-white bg-transparent p-2 rounded-full hover:bg-gray-800 transition duration-200"
      aria-label="Go back"
    >
      <FaArrowLeft className="w-6 h-6" />
    </button>
  );
};

export default BackButton
