import React, { useState, useEffect } from "react";
import { File, Folder, ExternalLink } from "lucide-react";
import { open } from "@tauri-apps/plugin-dialog";

interface BrowseWindowProps {
  label?: string;
  extensions?: string[];
  multiple?: boolean;
  isDir?: boolean;
  onSelect: (filePath: string) => void;
  value: string;
  className?: string;
}

const BrowseWindow: React.FC<BrowseWindowProps> = ({ label, extensions = [], multiple = false, isDir = false, onSelect, value, className = "" }) => {
  const [path, setPath] = useState<string | null>(value || null);

  useEffect(() => {
    setPath(value || null);
  }, [value]);

  const handleSelect = async () => {
    try {
      const selected = await open({
        directory: isDir,
        multiple: multiple,
        filters: [
          {
            name: `${extensions.join(", ").toUpperCase()} Files`,
            extensions,
          },
        ],
      });

      if (typeof selected === "string") {
        const isValid = isDir || extensions.some((ext) => selected.endsWith(`.${ext}`));
        if (isValid) {
          setPath(selected);
          onSelect(selected);
        } else {
          alert(`Please upload a valid .${extensions.join(" / .")} file.`);
        }
      }
    } catch (err) {
      console.error("Error on browsing:", err);
    }
  };

  return (
    <div className="w-full">
      {label && (
        <label className="block mb-2 text-sm font-medium text-gray-300">
          {label} <span className="text-indigo-400">*</span>
        </label>
      )}
      <button
        type="button"
        onClick={handleSelect}
        className={`flex items-center justify-between w-full px-3 py-2.5 border ${path ? "border-indigo-500/50" : "border-gray-700/50"} 
          ${className || "bg-gray-800/50 hover:bg-gray-700/50"} 
          rounded-md text-sm text-gray-300 transition-colors duration-200 truncate focus:outline-none focus:ring-2 focus:ring-indigo-500/30`}
      >
        <span className="flex items-center gap-2 truncate">
          {isDir ? (
            <Folder className="h-4 w-4 text-indigo-400 flex-shrink-0" />
          ) : (
            <File className="h-4 w-4 text-indigo-400 flex-shrink-0" />
          )}
          <span className="truncate">
            {path || `Select a ${isDir ? "folder" : "file"}...`}
          </span>
        </span>
        <ExternalLink className="h-4 w-4 text-gray-400 flex-shrink-0 ml-2" />
      </button>
    </div>
  );
};

export default BrowseWindow;
