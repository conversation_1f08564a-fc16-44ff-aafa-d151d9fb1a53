import React from "react";
import { motion } from "framer-motion";
import { AlertTriangle } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ConfirmationDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({ isOpen, title, message, onConfirm, onCancel }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="bg-melodyze-secondary/95 backdrop-blur-xl border-2 border-melodyze-pink/30 rounded-2xl shadow-2xl font-inter max-w-md">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
        >
          <DialogHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
              className="flex justify-center"
            >
              <AlertTriangle className="w-16 h-16 text-melodyze-pink" />
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3, duration: 0.5 }}>
              <DialogTitle className="text-2xl font-iceland font-bold bg-gradient-to-r from-melodyze-pink to-melodyze-purple bg-clip-text text-transparent">
                {title}
              </DialogTitle>
            </motion.div>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4, duration: 0.5 }}>
              <DialogDescription className="text-gray-200 text-lg leading-relaxed">{message}</DialogDescription>
            </motion.div>
          </DialogHeader>
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.5, duration: 0.5 }}>
            <DialogFooter className="flex justify-end space-x-4 mt-8">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant="outline"
                  onClick={onCancel}
                  className="px-6 py-3 font-inter font-medium border-melodyze-cyan/30 text-gray-300 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 transition-all duration-300"
                >
                  Cancel
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  onClick={onConfirm}
                  className="px-6 py-3 font-inter font-semibold bg-gradient-to-r from-melodyze-pink to-melodyze-purple hover:from-melodyze-pink/80 hover:to-melodyze-purple/80 transition-all duration-300 shadow-lg hover:shadow-melodyze-pink/25"
                >
                  Confirm
                </Button>
              </motion.div>
            </DialogFooter>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default ConfirmationDialog;
