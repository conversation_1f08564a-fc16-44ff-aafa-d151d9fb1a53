"use client";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Trash2, X } from "lucide-react";
import { IoCloseSharp } from "react-icons/io5";

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  title: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({ isOpen, title, onConfirm, onCancel }) => {
  const [typedInput, setTypedInput] = useState("");
  const handleCancel = () => {
    setTypedInput("");
    onCancel();
  };

  const handleConfirm = () => {
    setTypedInput("");
    onConfirm();
  };
  if (!isOpen) {
    return null;
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-md z-50 flex justify-center items-center font-inter"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="bg-melodyze-secondary/95 backdrop-blur-xl border-2 border-red-500/30 rounded-2xl p-8 shadow-2xl relative max-w-md w-full mx-4"
            initial={{ scale: 0.8, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 50 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            style={{
              boxShadow: "0 25px 50px rgba(0, 0, 0, 0.5), 0 0 50px rgba(239, 68, 68, 0.1)",
            }}
          >
            {/* Close Icon */}
            <motion.button
              className="absolute top-4 right-4 text-gray-400 hover:text-red-400 transition-colors duration-300"
              onClick={handleCancel}
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <X className="w-6 h-6" />
            </motion.button>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="text-center space-y-6"
            >
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 0.3, type: "spring", stiffness: 300 }}
                className="flex justify-center"
              >
                <Trash2 className="w-16 h-16 text-red-500" />
              </motion.div>

              <motion.h2
                className="text-2xl font-iceland font-bold bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                {title}
              </motion.h2>

              <motion.div
                className="h-px bg-gradient-to-r from-transparent via-red-500/50 to-transparent"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                className="space-y-4"
              >
                <p className="text-gray-200 text-lg leading-relaxed">
                  To confirm deletion, type <span className="font-bold text-red-400">delete</span> in the text input field.
                </p>

                <p className="text-sm italic text-gray-400">Note: This action cannot be undone.</p>

                {/* Input Field */}
                <motion.input
                  type="text"
                  className="w-full bg-melodyze-tertiary/50 border-2 border-red-500/30 rounded-xl p-4 text-gray-200 placeholder-gray-500 focus:border-red-500/60 focus:outline-none transition-all duration-300"
                  placeholder="Type 'delete' to confirm"
                  value={typedInput}
                  onChange={(e) => setTypedInput(e.target.value)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                />

                {/* Confirm Button */}
                <motion.div
                  className="flex justify-end pt-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8, duration: 0.5 }}
                >
                  <motion.button
                    className={`px-8 py-3 rounded-xl font-semibold transition-all duration-300 ${
                      typedInput.toLowerCase() === "delete"
                        ? "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-red-500/25 hover:from-red-600 hover:to-red-700"
                        : "bg-gray-600 text-gray-400 cursor-not-allowed"
                    }`}
                    onClick={handleConfirm}
                    disabled={typedInput.toLowerCase() !== "delete"}
                    whileHover={typedInput.toLowerCase() === "delete" ? { scale: 1.02 } : {}}
                    whileTap={typedInput.toLowerCase() === "delete" ? { scale: 0.98 } : {}}
                  >
                    Delete
                  </motion.button>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DeleteConfirmationDialog;
