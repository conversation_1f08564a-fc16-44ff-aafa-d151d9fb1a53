import React from "react";
import { motion } from "framer-motion";

interface AppLoaderProps {
  loadingText?: string;
  className?: string;
}

const AppLoader: React.FC<AppLoaderProps> = ({ 
  loadingText = "Initializing", 
  className = "" 
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-b from-gray-900 to-gray-950 flex flex-col items-center justify-center ${className}`}>
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-purple-900/20 to-pink-900/20 animate-pulse" />
      
      {/* Main loader container */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 flex flex-col items-center space-y-8"
      >
        {/* Neon spinning loader */}
        <div className="relative">
          {/* Outer ring */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-20 h-20 border-4 border-transparent border-t-cyan-400 border-b-pink-500 rounded-full"
            style={{
              boxShadow: "0 0 20px rgba(34, 211, 238, 0.5), 0 0 20px rgba(236, 72, 153, 0.5)"
            }}
          />
          
          {/* Inner ring */}
          <motion.div
            animate={{ rotate: -360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            className="absolute inset-2 w-12 h-12 border-2 border-transparent border-l-purple-400 border-r-indigo-400 rounded-full"
            style={{
              boxShadow: "0 0 15px rgba(147, 51, 234, 0.4), 0 0 15px rgba(99, 102, 241, 0.4)"
            }}
          />
          
          {/* Center dot */}
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
            className="absolute inset-0 m-auto w-2 h-2 bg-gradient-to-r from-cyan-400 to-pink-500 rounded-full"
            style={{
              boxShadow: "0 0 10px rgba(34, 211, 238, 0.8)"
            }}
          />
        </div>

        {/* Loading text */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="text-center space-y-2"
        >
          <p className="text-lg font-medium bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-500 bg-clip-text text-transparent">
            {loadingText}
          </p>
          
          {/* Animated dots */}
          <motion.div
            className="flex justify-center space-x-1"
          >
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                animate={{ 
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeInOut"
                }}
                className="w-1.5 h-1.5 bg-cyan-400 rounded-full"
              />
            ))}
          </motion.div>
        </motion.div>

        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-purple-500/10 to-pink-500/10 rounded-full blur-3xl scale-150" />
      </motion.div>
    </div>
  );
};

export default AppLoader;
