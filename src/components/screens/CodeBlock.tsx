"use client";

import { useState, useEffect } from "react";
import { createHighlighter } from "shiki";

export default function CodeBlock({ code, language }: { code: string; language: string }) {
  const [highlightedCode, setHighlightedCode] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const highlight = async () => {
      const highlighter = await createHighlighter({
        themes: ["github-dark"],
        langs: ["javascript", "typescript", "python", "bash"],
      });

      const html = highlighter.codeToHtml(code, {
        lang: language,
        theme: "github-dark",
      });

      setHighlightedCode(html);
      setIsLoading(false);
    };

    highlight();
  }, [code, language]);

  if (isLoading) {
    return (
      <pre className="bg-gray-800 p-4 rounded-lg animate-pulse">
        <code className="text-gray-400">Loading code...</code>
      </pre>
    );
  }

  return <div className="rounded-lg overflow-hidden my-4" dangerouslySetInnerHTML={{ __html: highlightedCode }} />;
}
