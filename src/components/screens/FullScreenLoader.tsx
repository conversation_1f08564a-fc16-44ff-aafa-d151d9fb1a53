import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/utils/ui";

interface FullScreenLoaderProps {
  isOpen: boolean;
  message: string;
  progress: number; // 0 to 100
}

const FullScreenLoader: React.FC<FullScreenLoaderProps> = ({ isOpen, message, progress }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/90 backdrop-blur-md flex flex-col items-center justify-center z-50 font-inter"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Circular Spinner with Neon Effect */}
          <motion.div
            className="flex items-center justify-center mb-8"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
          >
            <div className="relative">
              <motion.div
                className="w-20 h-20 border-4 border-melodyze-cyan/30 border-t-melodyze-cyan rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                aria-label="Loading"
              />
              <motion.div
                className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-melodyze-purple rounded-full"
                animate={{ rotate: -360 }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              />
              <div className="absolute inset-0 w-20 h-20 rounded-full bg-gradient-to-r from-melodyze-cyan/20 to-melodyze-purple/20 blur-xl" />
            </div>
          </motion.div>

          {/* Loader Message */}
          <motion.div
            className="text-white text-xl font-iceland font-bold mb-6 bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            {message}
          </motion.div>

          {/* Progress Bar */}
          <motion.div
            className="w-3/4 max-w-md"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <div className="relative">
              <Progress
                value={progress}
                className="h-3 bg-melodyze-secondary/50 border border-melodyze-cyan/20 rounded-full overflow-hidden"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-melodyze-cyan/20 to-melodyze-purple/20 rounded-full blur-sm" />
            </div>
          </motion.div>

          {/* Progress Percentage */}
          <motion.div
            className="text-melodyze-cyan font-semibold mt-4 text-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            {progress}%
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FullScreenLoader;
