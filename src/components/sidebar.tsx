"use client";
import { useState, useEffect } from "react";
import { Upload, Menu, Settings2, ActivityIcon, GuitarIcon, ChevronRight } from "lucide-react";
import Image from "next/image";
import { MeloButton } from "@animesh-melodyze/melodyze-ts-ui-modules";
import MeloLogo from "@/assests/melo_128x128.png";

export type ViewType = "home" | "upload" | "transfer" | "management" | "twelve-scale-render";

type SidebarItemType = {
  label: string;
  icon: React.ReactNode;
  route: ViewType;
};

type SidebarSectionType = {
  sectionLabel: string;
  items: SidebarItemType[];
};

// Create reusable sidebar items data structure
export const sidebarItems: SidebarSectionType[] = [
  {
    sectionLabel: "STYLE",
    items: [
      {
        label: "Upload style",
        icon: <Upload size={20} />,
        route: "upload",
      },
      {
        label: "Transfer Styles",
        icon: <GuitarIcon size={20} />,
        route: "transfer",
      },
      {
        label: "Manage Styles",
        icon: <Settings2 size={20} />,
        route: "management",
      },
    ],
  },
  {
    sectionLabel: "RENDER",
    items: [
      {
        label: "Twelve Scale Render",
        icon: <ActivityIcon size={20} />,
        route: "twelve-scale-render",
      },
    ],
  },
];

type SidebarProps = {
  currentPage: ViewType;
  onNavigate: (target: ViewType) => void;
  onOpenReaper: () => void;
  onLogoClick: () => void;
};

export default function Sidebar({ currentPage, onNavigate, onOpenReaper, onLogoClick }: SidebarProps) {
  const [expanded, setExpanded] = useState(true);

  const toggleSidebar = () => {
    setExpanded(!expanded);
  };

  useEffect(() => {
    const root = document.documentElement;
    root.style.setProperty("--sidebar-width", expanded ? "16rem" : "4rem");
  }, [expanded]);

  return (
    <div
      className="h-screen flex flex-col relative backdrop-blur-xl border-r border-white/10 transition-all duration-500 ease-in-out"
      style={{
        background: "var(--sidebar-gradient)",
        width: expanded ? "16rem" : "4rem",
      }}
    >
      {/* App Logo */}
      <div
        className="flex items-center py-6 cursor-pointer mb-6 transition-all duration-500 ease-in-out"
        onClick={onLogoClick}
        style={{
          justifyContent: expanded ? "flex-start" : "center",
          paddingLeft: expanded ? "1.5rem" : "0",
          paddingRight: expanded ? "1.5rem" : "0",
        }}
      >
        <div className="relative w-12 h-12 flex-shrink-0">
          <Image src={MeloLogo} alt="Melodyze Logo" fill className="object-contain drop-shadow-lg" />
        </div>
        <div
          className="overflow-hidden transition-all duration-500 ease-in-out"
          style={{
            width: expanded ? "auto" : "0",
            marginLeft: expanded ? "1rem" : "0",
            opacity: expanded ? 1 : 0,
          }}
        >
          <span style={{ fontSize: "1.5rem" }} className="font-iceland font-semibold text-white whitespace-nowrap tracking-wider block">
            Melodyze.ai
          </span>
        </div>
      </div>

      {/* Toggle Button */}
      <div
        className="mb-8 transition-all duration-500 ease-in-out"
        style={{
          paddingLeft: expanded ? "1rem" : "0.5rem",
          paddingRight: expanded ? "1rem" : "0.5rem",
        }}
      >
        <button
          onClick={toggleSidebar}
          className="p-3 text-gray-300 hover:text-white rounded-xl transition-all duration-300 ease-in-out
            hover:bg-white/10 backdrop-blur-sm border border-white/5 hover:border-white/20"
          style={{
            width: expanded ? "calc(100% - 2rem)" : "3rem",
            marginLeft: expanded ? "1rem" : "0rem",
            marginRight: expanded ? "1rem" : "0rem",
          }}
        >
          <div
            className="transition-transform duration-500 ease-in-out flex items-center justify-center"
            style={{ transform: expanded ? "rotate(0deg)" : "rotate(180deg)" }}
          >
            <Menu size={20} />
          </div>
        </button>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {sidebarItems.map((section, sectionIndex) => (
          <div key={sectionIndex} className="mb-6">
            {/* Section Header */}
            <div
              className="transition-all duration-500 ease-in-out"
              style={{ paddingLeft: expanded ? "1.5rem" : "1rem", paddingRight: expanded ? "1.5rem" : "1rem" }}
            >
              {expanded ? (
                <div className="mb-3">
                  <h3
                    className="text-xs font-inter font-semibold text-gray-400 uppercase tracking-wider transition-opacity duration-500 ease-in-out"
                    style={{ opacity: expanded ? 1 : 0 }}
                  >
                    {section.sectionLabel}
                  </h3>
                </div>
              ) : (
                <div className="mb-3 flex items-center">
                  <div
                    className="w-full h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent transition-opacity duration-500 ease-in-out"
                    style={{ opacity: expanded ? 0 : 1 }}
                  />
                </div>
              )}
            </div>

            {/* Navigation Items */}
            <ul className="space-y-1">
              {section.items.map((item, itemIndex) => (
                <li key={itemIndex}>
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onNavigate(item.route);
                    }}
                    className={`
                      flex items-center py-3 rounded-xl group relative overflow-hidden
                      transition-all duration-300 ease-in-out font-inter font-medium
                      ${
                        currentPage === item.route
                          ? "bg-melodyze-cyan/15 text-melodyze-cyan shadow-lg border border-melodyze-cyan/20"
                          : "text-gray-300 hover:text-white hover:bg-white/8 hover:shadow-md"
                      }
                    `}
                    style={{
                      marginLeft: expanded ? "0.5rem" : "0.5rem",
                      marginRight: expanded ? "0.5rem" : "0.5rem",
                      paddingLeft: expanded ? "1rem" : "0",
                      paddingRight: expanded ? "1rem" : "0",
                      justifyContent: expanded ? "flex-start" : "center",
                    }}
                  >
                    {/* Background glow effect for active item */}
                    {currentPage === item.route && (
                      <div className="absolute inset-0 bg-gradient-to-r from-melodyze-cyan/10 to-melodyze-purple/10 rounded-xl transition-opacity duration-300 ease-in-out" />
                    )}

                    {/* Icon - Always centered when collapsed */}
                    <span
                      className={`flex-shrink-0 relative z-10 transition-colors duration-300 ease-in-out ${
                        currentPage === item.route ? "text-melodyze-cyan" : "text-melodyze-indigo"
                      }`}
                    >
                      {item.icon}
                    </span>

                    {/* Label - Only show when expanded with smooth transition */}
                    <div
                      className="overflow-hidden transition-all duration-500 ease-in-out"
                      style={{
                        width: expanded ? "auto" : "0",
                        marginLeft: expanded ? "0.75rem" : "0",
                        opacity: expanded ? 1 : 0,
                      }}
                    >
                      <span className="whitespace-nowrap relative z-10 text-sm block">{item.label}</span>
                    </div>

                    {/* Hover indicator - Only when expanded */}
                    <div
                      className="absolute right-2 opacity-0 group-hover:opacity-50 transition-all duration-300 ease-in-out"
                      style={{
                        transform: expanded ? "translateX(0)" : "translateX(20px)",
                        opacity: expanded && currentPage !== item.route ? "inherit" : "0",
                      }}
                    >
                      <ChevronRight size={16} />
                    </div>
                  </a>

                  {/* Divider - Only show when expanded with smooth transition */}
                  {itemIndex < section.items.length - 1 && (
                    <div
                      className="px-6 py-1 transition-all duration-500 ease-in-out"
                      style={{
                        opacity: expanded ? 1 : 0,
                        height: expanded ? "auto" : "0",
                        overflow: "hidden",
                      }}
                    >
                      <div className="h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent" />
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* Bottom Action Button */}
      <div
        className="border-t border-white/10 transition-all duration-500 ease-in-out overflow-hidden"
        style={{
          padding: expanded ? "1.5rem" : "0",
          height: expanded ? "auto" : "0",
          opacity: expanded ? 1 : 0,
        }}
      >
        <div
          className="transition-all duration-500 ease-in-out"
          style={{
            transform: expanded ? "translateY(0)" : "translateY(20px)",
            opacity: expanded ? 1 : 0,
          }}
        >
          <MeloButton
            textStyle={{ fontFamily: "Inter", fontSize: "12px", fontWeight: "500" }}
            onPressed={onOpenReaper}
            text="Open Reaper"
            height={32}
            minHeight={28}
            width="85%"
            innerPadding="8px"
          />
        </div>
      </div>
    </div>
  );
}
