# Shadcn UI Components

This directory contains UI components built with shadcn UI, a collection of reusable components built using Radix UI and Tailwind CSS.

## Available Components

- **Button**: A versatile button component with multiple variants.
- **Card**: A container component for grouping related content.
- **Checkbox**: A form control for selecting multiple options.
- **Dialog**: A modal dialog component for confirmations and messages.
- **Input**: A form input component for collecting user data.
- **Label**: A form label component for accessibility.
- **Progress**: A progress indicator component.
- **RadioGroup**: A form control for selecting a single option from a list.
- **Select**: A form control for selecting an option from a dropdown list.
- **Switch**: A form control for toggling between two states.
- **Tabs**: A component for organizing content into tabbed sections.
- **Textarea**: A form control for multi-line text input.
- **Toast**: A component for displaying non-intrusive notifications.
- **Tooltip**: A component for displaying additional information on hover.

## Usage

Import components directly from their respective files:

```tsx
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
```

See the UI test page at `/src/app/ui-test/page.tsx` for examples of how to use these components.

## Styling

These components use Tailwind CSS for styling and are configured to use the application's theme defined in `globals.css`. The color scheme is based on CSS variables, making it easy to customize the appearance of the components.

## Utilities

The components use the `cn` utility function from `@/utils/ui` for class name composition, which combines `clsx` and `tailwind-merge` to handle class name conflicts properly.

## Hooks

Some components have associated hooks for managing state:

- **useToast**: A hook for managing toast notifications.

```tsx
import { useToast } from '@/hooks/use-toast';

function MyComponent() {
  const { toast } = useToast();
  
  const showToast = () => {
    toast({
      title: "Toast Title",
      description: "Toast description",
    });
  };
  
  return <Button onClick={showToast}>Show Toast</Button>;
}
```

## Adding New Components

To add a new component:

1. Create a new file in this directory with the component name (e.g., `select.tsx`).
2. Import the necessary dependencies and the `cn` utility.
3. Define the component using the shadcn UI pattern.
4. Export the component.

## References

- [shadcn UI](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)