export interface StyleBean {
    _id: string;
    audio_path: string;
    audio_with_vocals_path: string;
    genre: string;
    source: string;
    scale: string;
    tempo: string;
    annotator: string;
    time_signature: string;
    swing: boolean;
    pitch: string;
    section: string;
    duration_in_bars: string;
    raw_style_id: string;
    midi_zip_path: string;
    project_zip_path: string;
    project_ref_id?: string;
  }

export const SongPitchList = [
    "Major",
    "Minor",
]

export const SongPitchKeyMap:{[key:string]: string} = {
    "Major": "major",
    "Minor": "minor",
}

export const AnnotatorList = [
"Arindam", "Ayon", "Nirupam"
]