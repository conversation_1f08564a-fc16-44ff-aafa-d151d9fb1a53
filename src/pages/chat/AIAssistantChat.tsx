"use client";
import { useEffect, useRef, useState } from "react";
import websocketService from "@/utils/websocketService";
import ChatHeader from "./ChatHeader";
import ChatUploadPrompt from "./ChatUploadPrompt";
import ChatMessages, { AIMessage } from "./ChatMessages";
import ChatInput from "./ChatInput";
import ToolCallDisplay from "./ToolCallDisplay";
import StatusPanel, { StatusMessage } from "./StatusPanel";
import { Bot } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import CodeBlock from "@/components/screens/CodeBlock";
import { motion, AnimatePresence } from "framer-motion";

export default function AIAssistantChat() {
  const [chatInput, setChatInput] = useState("");
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialPromptSent, setInitialPromptSent] = useState(false);
  const [rppFilepath, setRppFilepath] = useState("");
  const [midiFilepath, setMidiFilepath] = useState("");
  const [outputDir, setOutputDir] = useState("");
  const [statusMessages, setStatusMessages] = useState<StatusMessage[]>([]);
  const [wsConnected, setWsConnected] = useState(websocketService.getIsConnected());
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  // Streaming state
  const [streamingContent, setStreamingContent] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentToolCalls, setCurrentToolCalls] = useState<any[]>([]);
  const [streamingEnabled, setStreamingEnabled] = useState(true);

  const scrollToBottom = () => messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });

  const handleSend = async () => {
    if (!chatInput.trim()) return;
    setLoading(true);
    setChatInput("");

    // Reset streaming state
    setStreamingContent("");
    setCurrentToolCalls([]);

    setMessages((prev) => [...prev, { role: "user", content: chatInput }]);

    if (wsConnected) {
      websocketService.sendChatMessage(chatInput, streamingEnabled);
    }
  };

  const handleNewAnalysis = async () => {
    setRppFilepath("");
    setMidiFilepath("");
    setOutputDir("");
    setMessages([]);
    setStatusMessages([]);
    setInitialPromptSent(false);
    setChatInput("");
    console.log("handleNewAnalysis:wsConnected", wsConnected);
    if (wsConnected) {
      websocketService.resetChat();
    }
  };

  const handleFileUpload = async () => {
    setLoading(true);
    console.log("handleFileUpload:wsConnected", wsConnected);
    if (wsConnected) {
      websocketService.uploadFile(rppFilepath, midiFilepath, outputDir);
      setInitialPromptSent(true);
    }
  };

  // Setup WebSocket connection and message handlers
  useEffect(() => {
    const initializeWebSocket = async () => {
      try {
        // Register status change handler
        const connStatusUnsubscribe = websocketService.onStatusChange((status) => {
          console.log("websocketService.onStatusChange", status);
          setWsConnected(status === "connected");
          if (status === "connected") {
            setStatusMessages((prev) => [...prev, { msg: "Connected to server", timestamp: Date.now() }]);
          } else if (status === "disconnected") {
            setStatusMessages((prev) => [...prev, { msg: "Disconnected from server", timestamp: Date.now() }]);
          }
        });

        await websocketService.connect();

        // Register message handlers for different message types
        const chatResponseUnsubscribe = websocketService.onMessage("chat_response", (message) => {
          setMessages(message.conversation);
          setLoading(false);
          setIsStreaming(false);
        });

        const fileUploadResponseUnsubscribe = websocketService.onMessage("file_upload_response", (message) => {
          setMessages(message.conversation);
          setLoading(false);
        });

        const resetResponseUnsubscribe = websocketService.onMessage("reset_response", (message) => {
          setMessages(message.conversation);
        });

        const statusUnsubscribe = websocketService.onMessage("status", (message) => {
          setStatusMessages((prev) => [...prev, { msg: message.message, timestamp: Date.now() }]);
        });

        const statusUpdateUnsubscribe = websocketService.onMessage("status_update", (message) => {
          setStatusMessages((prev) => [...prev, message.message]);
        });

        const errorUnsubscribe = websocketService.onMessage("error", (message) => {
          setStatusMessages((prev) => [...prev, { msg: `Error: ${message.message}`, timestamp: Date.now() }]);
          setLoading(false);
          setIsStreaming(false);
        });

        // Streaming message handlers
        const streamingStartUnsubscribe = websocketService.onMessage("streaming_start", (message) => {
          setIsStreaming(true);
          setStreamingContent("");
          setCurrentToolCalls([]);
          setStatusMessages((prev) => [...prev, { msg: `Streaming started: ${message.message}`, timestamp: Date.now() }]);
        });

        const streamingEndUnsubscribe = websocketService.onMessage("streaming_end", (message) => {
          setIsStreaming(false);
          setStatusMessages((prev) => [...prev, { msg: `Streaming complete: ${message.message}`, timestamp: Date.now() }]);
        });

        const streamingUpdateUnsubscribe = websocketService.onMessage("streaming_update", (message) => {
          if (message.update_type === "content_chunk") {
            setStreamingContent((prev) => prev + message.content);
          } else if (message.update_type === "tool_call_name") {
            const index = message.data?.index || 0;
            setCurrentToolCalls((prev) => {
              const updated = [...prev];
              // Initialize if needed
              while (updated.length <= index) {
                updated.push({ name: "", arguments: "" });
              }
              updated[index] = { ...updated[index], name: message.content };
              return updated;
            });
          } else if (message.update_type === "tool_call_args") {
            const index = message.data?.index || 0;
            setCurrentToolCalls((prev) => {
              const updated = [...prev];
              // Initialize if needed
              while (updated.length <= index) {
                updated.push({ name: "", arguments: "" });
              }
              updated[index] = {
                ...updated[index],
                name: updated[index].name || message.data?.name || "",
                arguments: (updated[index].arguments || "") + message.content,
              };
              return updated;
            });
          }
        });

        // Cleanup function
        return () => {
          connStatusUnsubscribe();
          statusUnsubscribe();
          chatResponseUnsubscribe();
          fileUploadResponseUnsubscribe();
          resetResponseUnsubscribe();
          statusUpdateUnsubscribe();
          errorUnsubscribe();
          streamingStartUnsubscribe();
          streamingEndUnsubscribe();
          streamingUpdateUnsubscribe();
          websocketService.disconnect();
        };
      } catch (error) {
        console.error("Failed to connect to WebSocket server:", error);
      }
    };

    initializeWebSocket();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => scrollToBottom(), [messages, statusMessages]);

  // Initialize chat on component mount
  // useEffect(() => {
  //   handleNewAnalysis();
  // }, []);

  // Handle file upload when RPP filepath changes
  useEffect(() => {
    if (rppFilepath) {
      handleFileUpload();
    }
  }, [rppFilepath]);

  const allFilesSelected = !!rppFilepath; //&& midiFilepath && outputDir);

  return (
    <div className="min-h-screen bg-gradient-to-br from-melodyze-secondary via-melodyze-tertiary to-melodyze-secondary flex flex-col font-inter">
      {allFilesSelected && (
        <ChatHeader
          rppFilename={rppFilepath.split(/[\\/]/).pop()!}
          onReset={handleNewAnalysis}
          streamingEnabled={streamingEnabled}
          onStreamingToggle={() => setStreamingEnabled(!streamingEnabled)}
        />
      )}

      <main className="flex-1 overflow-hidden relative">
        <div className="h-full overflow-y-auto pt-16 pb-24 px-4 md:px-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {!allFilesSelected ? (
              <ChatUploadPrompt
                rppFilepath={rppFilepath}
                midiFilepath={midiFilepath}
                outputDir={outputDir}
                onRppSelect={setRppFilepath}
                onMidiSelect={setMidiFilepath}
                onDirSelect={setOutputDir}
              />
            ) : (
              <div className="space-y-6">
                <ChatMessages messages={messages} loading={loading} endRef={messagesEndRef} />

                {/* Streaming Content */}
                <AnimatePresence>
                  {isStreaming && streamingContent && (
                    <motion.div
                      className="flex gap-6"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.4, type: "spring", stiffness: 300 }}
                    >
                      <motion.div
                        className="h-12 w-12 rounded-full border-2 border-melodyze-cyan/40 flex items-center justify-center backdrop-blur-sm"
                        animate={{
                          boxShadow: [
                            "0 0 0 0 rgba(34, 211, 238, 0.4)",
                            "0 0 0 10px rgba(34, 211, 238, 0)",
                            "0 0 0 0 rgba(34, 211, 238, 0)",
                          ],
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <div className="bg-gradient-to-br from-melodyze-cyan to-melodyze-purple rounded-full p-2">
                          <motion.div animate={{ rotate: 360 }} transition={{ duration: 3, repeat: Infinity, ease: "linear" }}>
                            <Bot className="h-6 w-6 text-white" />
                          </motion.div>
                        </div>
                      </motion.div>
                      <motion.div
                        className="max-w-[80%] p-6 rounded-2xl bg-melodyze-secondary/60 border border-melodyze-cyan/20 shadow-lg backdrop-blur-xl"
                        style={{
                          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
                        }}
                        initial={{ scale: 0.95 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="prose prose-invert prose-sm max-w-none prose-headings:text-melodyze-cyan prose-p:text-gray-200 prose-a:text-melodyze-pink prose-strong:text-melodyze-cyan prose-code:text-melodyze-purple">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              code({ node, className, children, className: cls, ...props }) {
                                const match = /language-(\w+)/.exec(cls || "");
                                return match ? (
                                  <CodeBlock code={String(children).replace(/\n$/, "")} language={match[1]} />
                                ) : (
                                  <code className="bg-melodyze-tertiary/50 px-2 py-1 rounded-md text-melodyze-cyan font-mono" {...props}>
                                    {children}
                                  </code>
                                );
                              },
                            }}
                          >
                            {streamingContent}
                          </ReactMarkdown>
                        </div>
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Tool Calls */}
                <AnimatePresence>
                  {isStreaming && currentToolCalls.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ToolCallDisplay toolCalls={currentToolCalls} />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Status Panel */}
      <StatusPanel statusMessages={statusMessages} />

      {allFilesSelected && (
        <motion.div
          className="fixed bottom-0 left-[var(--sidebar-width)] right-0 z-10 bg-gradient-to-t from-melodyze-secondary via-melodyze-secondary/90 to-transparent pt-8 backdrop-blur-md"
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, type: "spring", stiffness: 300 }}
        >
          <div className="max-w-4xl mx-auto px-4 pb-4">
            <ChatInput value={chatInput} onChange={setChatInput} onSend={handleSend} loading={loading} />
          </div>
        </motion.div>
      )}
    </div>
  );
}
