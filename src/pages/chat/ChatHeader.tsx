import { Setting<PERSON>, Refresh<PERSON><PERSON>, Download, FileMusic, Zap, ZapOff } from "lucide-react";
import { motion } from "framer-motion";

interface ChatHeaderProps {
  rppFilename: string;
  onReset: () => void;
  streamingEnabled: boolean;
  onStreamingToggle: () => void;
}
export default function ChatHeader({ rppFilename, onReset, streamingEnabled, onStreamingToggle }: ChatHeaderProps) {
  return (
    <header className="mt-6">
      <motion.header
        className="fixed top-0 left-[var(--sidebar-width)] right-0 z-20 bg-gradient-to-r from-melodyze-secondary via-melodyze-tertiary to-melodyze-secondary border-b border-melodyze-cyan/20 backdrop-blur-xl shadow-lg font-inter"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, type: "spring", stiffness: 300 }}
        style={{
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
        }}
      >
        <div className="max-w-5xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            {/* Left side - Project info */}
            <motion.div
              className="flex items-center space-x-4"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.4 }}
            >
              <motion.div
                className="bg-gradient-to-br from-melodyze-cyan/20 to-melodyze-purple/20 p-3 rounded-xl border border-melodyze-cyan/30 backdrop-blur-sm"
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <FileMusic className="h-6 w-6 text-melodyze-cyan" />
              </motion.div>
              <div>
                <motion.h1
                  className="text-lg font-iceland font-bold bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent flex items-center"
                  whileHover={{ scale: 1.02 }}
                >
                  {rppFilename}
                </motion.h1>
                <p className="text-sm text-melodyze-cyan/70 font-medium">Melodyze AI Analysis</p>
              </div>
            </motion.div>

            {/* Right side - Actions */}
            <motion.div
              className="flex items-center space-x-3"
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              {/* Streaming toggle button */}
              <motion.button
                onClick={onStreamingToggle}
                className={`p-3 rounded-xl transition-all duration-300 backdrop-blur-sm border ${
                  streamingEnabled
                    ? "text-melodyze-cyan bg-melodyze-cyan/10 border-melodyze-cyan/40 hover:bg-melodyze-cyan/20 hover:border-melodyze-cyan/60"
                    : "text-melodyze-cyan/60 hover:text-melodyze-cyan border-melodyze-cyan/20 hover:border-melodyze-cyan/40 hover:bg-melodyze-cyan/10"
                }`}
                title={streamingEnabled ? "Disable streaming" : "Enable streaming"}
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {streamingEnabled ? (
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, -10, 0],
                      filter: [
                        "drop-shadow(0 0 0px rgba(34, 211, 238, 0.5))",
                        "drop-shadow(0 0 8px rgba(34, 211, 238, 0.8))",
                        "drop-shadow(0 0 12px rgba(34, 211, 238, 1))",
                        "drop-shadow(0 0 8px rgba(34, 211, 238, 0.6))",
                        "drop-shadow(0 0 0px rgba(34, 211, 238, 0.3))",
                      ],
                    }}
                    transition={{
                      duration: 0.6,
                      repeat: Infinity,
                      repeatDelay: 4.4,
                      ease: "easeInOut",
                    }}
                  >
                    <Zap className="h-5 w-5" />
                  </motion.div>
                ) : (
                  <ZapOff className="h-5 w-5" />
                )}
              </motion.button>
              <motion.button
                className="p-3 text-melodyze-cyan/60 hover:text-melodyze-cyan rounded-xl hover:bg-melodyze-cyan/10 transition-all duration-300 backdrop-blur-sm border border-melodyze-cyan/20 hover:border-melodyze-cyan/40"
                title="Download conversation"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Download className="h-5 w-5" />
              </motion.button>
              <motion.button
                className="p-3 text-melodyze-cyan/60 hover:text-melodyze-cyan rounded-xl hover:bg-melodyze-cyan/10 transition-all duration-300 backdrop-blur-sm border border-melodyze-cyan/20 hover:border-melodyze-cyan/40"
                title="Settings"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Settings className="h-5 w-5" />
              </motion.button>
              <motion.button
                onClick={onReset}
                className="flex items-center space-x-2 ml-2 bg-gradient-to-r from-melodyze-pink/20 to-red-500/20 hover:from-melodyze-pink/30 hover:to-red-500/30 border border-melodyze-pink/30 hover:border-melodyze-pink/50 px-4 py-2 rounded-xl text-melodyze-pink hover:text-white transition-all duration-300 backdrop-blur-sm"
                title="Start a new analysis"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
                  <RefreshCw className="h-4 w-4" />
                </motion.div>
                <span className="text-sm font-medium">New Analysis</span>
              </motion.button>
            </motion.div>
          </div>
        </div>
      </motion.header>
    </header>
  );
}
