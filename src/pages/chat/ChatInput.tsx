import { Send, Paperclip, Command } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface ChatInputProps {
  value: string;
  onChange: (val: string) => void;
  onSend: () => void;
  loading: boolean;
}

export default function ChatInput({ value, onChange, onSend, loading }: ChatInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const scrollHeight = textareaRef.current.scrollHeight;
      textareaRef.current.style.height = `${Math.min(scrollHeight, 200)}px`;
    }
  }, [value]);

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Send on Enter (without shift for new line)
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (value.trim()) onSend();
    }

    // Ctrl+Enter or Cmd+Enter also sends
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (value.trim()) onSend();
    }
  };

  return (
    <motion.div
      className="w-full font-inter"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.4, type: "spring", stiffness: 300 }}
    >
      {/* Keyboard shortcuts hint */}
      <AnimatePresence>
        {isFocused && (
          <motion.div
            className="flex justify-end mb-2 px-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center space-x-4 text-sm text-melodyze-cyan/70 bg-melodyze-secondary/50 px-4 py-2 rounded-xl backdrop-blur-md border border-melodyze-cyan/20">
              <motion.div className="flex items-center" whileHover={{ scale: 1.05 }}>
                <span className="px-2 py-1 rounded-lg bg-melodyze-cyan/20 text-melodyze-cyan font-mono mr-2 text-xs border border-melodyze-cyan/30">
                  Enter
                </span>
                <span>Send</span>
              </motion.div>
              <motion.div className="flex items-center" whileHover={{ scale: 1.05 }}>
                <span className="px-2 py-1 rounded-lg bg-melodyze-cyan/20 text-melodyze-cyan font-mono mr-2 text-xs border border-melodyze-cyan/30">
                  Shift + Enter
                </span>
                <span>New line</span>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className={`flex items-end gap-3 p-3 rounded-2xl bg-melodyze-secondary/80 border backdrop-blur-xl transition-all duration-300 ${
          isFocused ? "border-melodyze-cyan/50 shadow-lg shadow-melodyze-cyan/10" : "border-melodyze-cyan/20 hover:border-melodyze-cyan/30"
        }`}
        style={{
          boxShadow: isFocused ? "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.2)" : "0 4px 16px rgba(0, 0, 0, 0.2)",
        }}
        whileHover={{ scale: 1.01 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        {/* Feature buttons */}
        <motion.div
          className="flex space-x-2 mb-1 ml-1"
          initial={{ x: -10, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          <motion.button
            className="p-2 text-melodyze-cyan/50 hover:text-melodyze-cyan rounded-xl hover:bg-melodyze-cyan/10 transition-all duration-300 border border-melodyze-cyan/20 hover:border-melodyze-cyan/40 backdrop-blur-sm"
            title="Attach file (coming soon)"
            disabled
            whileHover={{ scale: 1.1, rotate: -15 }}
            whileTap={{ scale: 0.95 }}
          >
            <Paperclip className="h-5 w-5" />
          </motion.button>
        </motion.div>

        {/* Textarea input */}
        <motion.textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder="Ask about your project..."
          rows={1}
          className="flex-1 bg-transparent border-0 resize-none max-h-[200px] px-3 py-3 text-gray-100 placeholder:text-melodyze-cyan/40 focus:outline-none focus:ring-0 disabled:bg-transparent disabled:text-gray-500 scrollbar-thin scrollbar-thumb-melodyze-cyan/30 scrollbar-track-transparent font-inter"
          disabled={loading}
          initial={{ opacity: 0.8 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />

        {/* Send button */}
        <motion.button
          disabled={!value.trim() || loading}
          onClick={onSend}
          className="self-end p-3 rounded-xl bg-gradient-to-r from-melodyze-cyan to-melodyze-purple hover:from-melodyze-cyan/80 hover:to-melodyze-purple/80 text-white flex items-center justify-center transition-all duration-300 disabled:bg-melodyze-tertiary/50 disabled:text-gray-500 disabled:cursor-not-allowed shadow-lg"
          title="Send message (Enter)"
          whileHover={{ scale: 1.05, y: -2 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: "spring", stiffness: 300 }}
          style={{
            boxShadow: !value.trim() || loading ? "none" : "0 4px 20px rgba(34, 211, 238, 0.3)",
          }}
        >
          <Send className="h-5 w-5" />
        </motion.button>
      </motion.div>

      {/* Model info */}
      <div className="flex justify-center mt-2">
        <div className="flex items-center text-sm text-melodyze-cyan/60 bg-melodyze-secondary/30  rounded-xl backdrop-blur-sm">
          <Command className="h-4 w-4 mr-2 text-melodyze-cyan" />
          <span className="font-sm bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent">
            Powered by Melodyze AI
          </span>
        </div>
      </div>
    </motion.div>
  );
}
