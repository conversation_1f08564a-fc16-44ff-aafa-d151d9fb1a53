import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { AIMessage } from "./ChatMessages";
import CodeBlock from "@/components/screens/CodeBlock";
import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { motion, AnimatePresence } from "framer-motion";

export default function ChatMessageItem({ message }: { message: AIMessage }) {
  const [copied, setCopied] = useState(false);

  // Add timestamp if not present (for display purposes)
  const timestamp = message.timestamp || new Date().toISOString();
  const formattedTime = formatDistanceToNow(new Date(timestamp), { addSuffix: true });

  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  return (
    <motion.div
      className={`group flex gap-6 py-4 font-inter ${message.role === "user" ? "justify-end" : ""}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
    >
      {/* Avatar - only show on left for assistant messages */}
      {message.role !== "user" && (
        <motion.div
          className="h-12 w-12 shrink-0 rounded-full bg-gradient-to-br from-melodyze-cyan to-melodyze-purple flex items-center justify-center shadow-lg border border-melodyze-cyan/30"
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Bot className="h-6 w-6 text-white" />
        </motion.div>
      )}

      <div className="flex flex-col max-w-[80%] relative">
        {/* Message header with role and timestamp */}
        <motion.div
          className={`flex items-center mb-2 ${message.role === "user" ? "justify-end" : ""}`}
          initial={{ opacity: 0, x: message.role === "user" ? 10 : -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <span className="text-sm font-medium text-melodyze-cyan">{message.role === "user" ? "You" : "Assistant"}</span>
          <span className="mx-2 text-melodyze-cyan/50 text-sm">•</span>
          <span className="text-sm text-melodyze-cyan/70">{formattedTime}</span>
        </motion.div>

        {/* Message content */}
        <motion.div
          className={`relative p-4 rounded-2xl shadow-lg backdrop-blur-xl border ${
            message.role === "user"
              ? "bg-gradient-to-br from-melodyze-cyan/20 to-melodyze-purple/20 border-melodyze-cyan/30 rounded-tr-none"
              : "bg-melodyze-secondary/60 border-melodyze-cyan/20 rounded-tl-none"
          }`}
          style={{
            boxShadow:
              message.role === "user"
                ? "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.2)"
                : "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
          }}
          whileHover={{ scale: 1.01 }}
        >

          {/* Message content with improved markdown */}
          <motion.div
            className="prose prose-invert prose-sm max-w-none prose-headings:font-semibold prose-headings:text-melodyze-cyan prose-p:text-gray-200 prose-a:text-melodyze-pink prose-strong:text-melodyze-cyan prose-code:text-melodyze-purple prose-pre:bg-melodyze-tertiary/50 prose-pre:border prose-pre:border-melodyze-cyan/30 prose-pre:rounded-xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                code({ node, className, children, className: cls, ...props }) {
                  const match = /language-(\w+)/.exec(cls || "");
                  return match ? (
                    <CodeBlock code={String(children).replace(/\n$/, "")} language={match[1]} />
                  ) : (
                    <code
                      className="bg-melodyze-tertiary/50 px-2 py-1 rounded-md text-melodyze-cyan font-mono border border-melodyze-cyan/20"
                      {...props}
                    >
                      {children}
                    </code>
                  );
                },
                // Improve table styling
                table({ node, ...props }) {
                  return <table className="border-collapse border border-melodyze-cyan/30 rounded-xl overflow-hidden" {...props} />;
                },
                th({ node, ...props }) {
                  return (
                    <th
                      className="border border-melodyze-cyan/30 px-4 py-3 bg-melodyze-secondary/50 text-melodyze-cyan font-medium"
                      {...props}
                    />
                  );
                },
                td({ node, ...props }) {
                  return <td className="border border-melodyze-cyan/30 px-4 py-3 text-gray-200" {...props} />;
                },
                // Improve list styling
                ul({ node, ...props }) {
                  return <ul className="list-disc pl-6 space-y-2 marker:text-melodyze-cyan" {...props} />;
                },
                ol({ node, ...props }) {
                  return <ol className="list-decimal pl-6 space-y-2 marker:text-melodyze-cyan" {...props} />;
                },
                // Improve blockquote styling
                blockquote({ node, ...props }) {
                  return (
                    <blockquote
                      className="border-l-4 border-melodyze-cyan pl-4 italic text-melodyze-cyan/80 bg-melodyze-cyan/5 py-2 rounded-r-lg"
                      {...props}
                    />
                  );
                },
              }}
            >
              {message.content}
            </ReactMarkdown>
          </motion.div>
        </motion.div>
      </div>

      {/* Avatar - only show on right for user messages */}
      {message.role === "user" && (
        <motion.div
          className="h-12 w-12 shrink-0 rounded-full bg-gradient-to-br from-melodyze-pink to-melodyze-purple flex items-center justify-center shadow-lg border border-melodyze-pink/30"
          whileHover={{ scale: 1.1, rotate: -5 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <User className="h-6 w-6 text-white" />
        </motion.div>
      )}
    </motion.div>
  );
}
