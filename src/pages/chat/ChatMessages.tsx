import { Bot, MessageSquare } from "lucide-react";
import ChatMessageItem from "./ChatMessageItem";
import { motion, AnimatePresence } from "framer-motion";

export type AIMessage = {
  role: "user" | "assistant" | "system";
  content: any;
  timestamp?: string;
};

interface Props {
  messages: AIMessage[];
  loading: boolean;
  endRef: React.RefObject<HTMLDivElement>;
}

export default function ChatMessages({ messages, loading, endRef }: Props) {
  return (
    <div className="flex flex-col space-y-4 pb-6 font-inter">
      {messages.length === 0 && !loading && (
        <motion.div
          className="flex flex-col items-center justify-center py-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, type: "spring", stiffness: 300 }}
        >
          <motion.div
            className="bg-gradient-to-br from-melodyze-cyan/20 to-melodyze-purple/20 rounded-full p-6 mb-6 border border-melodyze-cyan/30 backdrop-blur-sm"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.div
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <MessageSquare className="h-10 w-10 text-melodyze-cyan" />
            </motion.div>
          </motion.div>
          <motion.h3
            className="text-2xl font-iceland font-bold bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent mb-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            No messages yet
          </motion.h3>
          <motion.p
            className="text-melodyze-cyan/70 max-w-md text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Start the conversation by sending a message below.
          </motion.p>
        </motion.div>
      )}

      <div className="space-y-3">
        <AnimatePresence>
          {messages.map(
            (msg, i) =>
              (msg.role === "assistant" || msg.role === "user") &&
              msg.content && (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{
                    duration: 0.4,
                    type: "spring",
                    stiffness: 300,
                    delay: i * 0.05,
                  }}
                >
                  <ChatMessageItem message={msg} />
                </motion.div>
              )
          )}
        </AnimatePresence>
      </div>

      <AnimatePresence>
        {loading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex gap-6 py-4"
            transition={{ duration: 0.4, type: "spring", stiffness: 300 }}
          >
            <motion.div
              className="h-12 w-12 shrink-0 rounded-full bg-gradient-to-br from-melodyze-cyan to-melodyze-purple flex items-center justify-center shadow-lg border border-melodyze-cyan/30"
              animate={{
                boxShadow: ["0 0 0 0 rgba(34, 211, 238, 0.4)", "0 0 0 10px rgba(34, 211, 238, 0)", "0 0 0 0 rgba(34, 211, 238, 0)"],
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <motion.div animate={{ rotate: 360 }} transition={{ duration: 3, repeat: Infinity, ease: "linear" }}>
                <Bot className="h-6 w-6 text-white" />
              </motion.div>
            </motion.div>
            <div className="flex flex-col max-w-[80%]">
              <motion.div className="flex items-center mb-2" initial={{ x: -10 }} animate={{ x: 0 }} transition={{ delay: 0.1 }}>
                <span className="text-sm font-medium text-melodyze-cyan">Assistant</span>
              </motion.div>
              <motion.div
                className="bg-melodyze-secondary/60 border border-melodyze-cyan/20 p-4 rounded-2xl rounded-tl-none shadow-lg backdrop-blur-xl"
                style={{
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
                }}
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <div className="flex space-x-2">
                  <motion.div
                    className="h-3 w-3 rounded-full bg-melodyze-cyan"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  <motion.div
                    className="h-3 w-3 rounded-full bg-melodyze-purple"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.2,
                    }}
                  />
                  <motion.div
                    className="h-3 w-3 rounded-full bg-melodyze-pink"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.4,
                    }}
                  />
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div ref={endRef} className="h-6" />
    </div>
  );
}
