import BrowseWindow from "@/components/dialog-box/BrowseWindow";
import { FolderOutput, Bo<PERSON>, FolderKanbanIcon, FileMusicIcon } from "lucide-react";
import { motion } from "framer-motion";

interface Props {
  rppFilepath: string;
  midiFilepath: string;
  outputDir: string;
  onRppSelect: (path: string) => void;
  onMidiSelect: (path: string) => void;
  onDirSelect: (path: string) => void;
}

export default function ChatUploadPrompt({ rppFilepath, midiFilepath, outputDir, onRppSelect, onMidiSelect, onDirSelect }: Props) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
    },
  };

  return (
    <motion.div
      className="flex flex-col justify-center items-center gap-10 py-16 font-inter"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div className="flex items-center justify-center mb-6" variants={itemVariants}>
        <motion.div
          className="bg-gradient-to-br from-melodyze-cyan/20 to-melodyze-purple/20 p-6 rounded-full border-2 border-melodyze-cyan/40 mr-6 backdrop-blur-sm"
          whileHover={{ scale: 1.1, rotate: 10 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <motion.div
            animate={{
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Bot className="h-10 w-10 text-melodyze-cyan" />
          </motion.div>
        </motion.div>
        <div>
          <motion.h1
            className="text-4xl font-iceland font-bold bg-gradient-to-r from-melodyze-cyan via-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            Welcome to Melodyze AI
          </motion.h1>
          <motion.p
            className="text-melodyze-cyan/70 mt-2 text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            Upload your project files to begin the analysis
          </motion.p>
        </div>
      </motion.div>

      <motion.div
        className="w-full max-w-lg bg-melodyze-secondary/60 backdrop-blur-xl p-8 rounded-2xl border border-melodyze-cyan/30 shadow-2xl"
        variants={itemVariants}
        style={{
          boxShadow: "0 20px 60px rgba(0, 0, 0, 0.4), 0 0 40px rgba(34, 211, 238, 0.1)",
        }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <div className="space-y-6">
          <motion.div className="space-y-2" variants={itemVariants}>
            <motion.label
              className="flex items-center text-sm font-medium text-gray-200 mb-2"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.div whileHover={{ rotate: 15, scale: 1.01 }} transition={{ type: "spring", stiffness: 300 }}>
                <FolderKanbanIcon className="h-5 w-5 mr-3 text-melodyze-cyan" />
              </motion.div>
              Reaper Project File (.rpp)
            </motion.label>
            <BrowseWindow
              label=""
              extensions={["rpp", "RPP"]}
              value={rppFilepath}
              onSelect={onRppSelect}
              className="bg-melodyze-tertiary/50 border-melodyze-cyan/30 hover:border-melodyze-cyan/50 focus:border-melodyze-cyan/50"
            />
            <p className="text-xs text-gray-400 mt-1">Select your Reaper project file (.rpp)</p>
          </motion.div>

          <motion.div className="space-y-2" variants={itemVariants}>
            <motion.label
              className="flex items-center text-sm font-medium text-gray-200 mb-2"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.div whileHover={{ rotate: -15, scale: 1.2 }} transition={{ type: "spring", stiffness: 300 }}>
                <FileMusicIcon className="h-5 w-5 mr-3 text-melodyze-cyan" />
              </motion.div>
              MIDI File (.mid)
            </motion.label>
            <BrowseWindow
              label=""
              extensions={["mid"]}
              value={midiFilepath}
              onSelect={onMidiSelect}
              className="bg-melodyze-tertiary/50 border-melodyze-cyan/30 hover:border-melodyze-cyan/50 focus:border-melodyze-cyan/50"
            />
            <p className="text-xs text-gray-400 mt-1">Select your MIDI file (.mid)</p>
          </motion.div>

          <motion.div className="space-y-2" variants={itemVariants}>
            <motion.label
              className="flex items-center text-sm font-medium text-gray-200 mb-2"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.div whileHover={{ rotate: 15, scale: 1.2 }} transition={{ type: "spring", stiffness: 300 }}>
                <FolderOutput className="h-5 w-5 mr-3 text-melodyze-cyan" />
              </motion.div>
              Output Directory
            </motion.label>
            <BrowseWindow
              label=""
              extensions={[]}
              value={outputDir}
              onSelect={onDirSelect}
              isDir={true}
              className="bg-melodyze-tertiary/50 border-melodyze-cyan/30 hover:border-melodyze-cyan/50 focus:border-melodyze-cyan/50"
            />
            <p className="text-xs text-gray-400 mt-1">Choose where to save the generated files</p>
          </motion.div>
        </div>

        <motion.div className="mt-8 text-center" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.8 }}>
          <p className="text-sm text-melodyze-cyan/60 font-medium">
            Melodyze AI will analyze your project and provide intelligent suggestions
          </p>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
