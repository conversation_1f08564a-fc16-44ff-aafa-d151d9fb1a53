"use client";
import { useState } from "react";
import { Bell, ChevronLeft } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export interface StatusMessage {
  msg: string;
  timestamp: number;
}

interface StatusPanelProps {
  statusMessages: StatusMessage[];
}

export default function StatusPanel({ statusMessages }: StatusPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (statusMessages.length === 0) return null;

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    
    if (diffMinutes < 1) return "Just now";
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return date.toLocaleTimeString();
  };

  return (
    <motion.div
      className="fixed right-0 top-1/2 -translate-y-1/2 z-30"
      initial={{ x: "100%" }}
      animate={{ x: isExpanded ? 0 : "calc(100% - 3rem)" }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      <div className="bg-gradient-to-l from-melodyze-secondary/95 via-melodyze-tertiary/90 to-melodyze-secondary/95 backdrop-blur-xl border-l border-melodyze-cyan/20 shadow-2xl h-auto max-h-[70vh] min-h-[8rem] w-80">
        {/* Toggle Button */}
        <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full">
          <div className="bg-gradient-to-r from-melodyze-secondary to-melodyze-tertiary border border-melodyze-cyan/20 rounded-l-xl p-2 shadow-lg backdrop-blur-md">
            <motion.div
              animate={{ rotate: isExpanded ? 0 : 180 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronLeft className="h-4 w-4 text-melodyze-cyan" />
            </motion.div>
          </div>
        </div>

        {/* Panel Content */}
        <div className="p-4 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4 pb-3 border-b border-melodyze-cyan/20">
            <Bell className="h-5 w-5 text-melodyze-cyan flex-shrink-0" />
            <AnimatePresence>
              {isExpanded && (
                <motion.h4
                  className="font-iceland text-lg bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  Status Updates
                </motion.h4>
              )}
            </AnimatePresence>
          </div>

          {/* Status Messages */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                className="flex-1 overflow-hidden"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-2 max-h-[50vh] overflow-y-auto scrollbar-thin scrollbar-thumb-melodyze-cyan/30 scrollbar-track-transparent pr-2">
                  {statusMessages.map((statusMsg, i) => (
                    <motion.div
                      key={i}
                      className="text-gray-300 py-3 px-3 rounded-lg bg-melodyze-tertiary/30 border border-melodyze-cyan/10 text-sm backdrop-blur-sm"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: i * 0.05 }}
                    >
                      <div className="flex flex-col gap-1">
                        <div className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-melodyze-cyan/60 mt-2 flex-shrink-0" />
                          <span className="leading-relaxed flex-1">{statusMsg.msg}</span>
                        </div>
                        <div className="text-xs text-gray-400 ml-4 mt-1">
                          {formatTimestamp(statusMsg.timestamp)}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Collapsed State Indicator */}
          <AnimatePresence>
            {!isExpanded && statusMessages.length > 0 && (
              <motion.div
                className="flex flex-col items-center justify-center flex-1 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-melodyze-cyan/60 text-xs font-medium mb-2">
                  {statusMessages.length} update{statusMessages.length !== 1 ? 's' : ''}
                </div>
                <div className="flex space-x-1">
                  {[...Array(Math.min(3, statusMessages.length))].map((_, i) => (
                    <div
                      key={i}
                      className="w-1.5 h-1.5 rounded-full bg-melodyze-cyan/40"
                    />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Glow Effect */}
        <div 
          className="absolute inset-0 -z-10 opacity-20"
          style={{
            background: "linear-gradient(90deg, transparent, rgba(34, 211, 238, 0.1), transparent)",
            filter: "blur(20px)"
          }}
        />
      </div>
    </motion.div>
  );
}