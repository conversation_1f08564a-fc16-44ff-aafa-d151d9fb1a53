import React, { useState } from "react";
import { <PERSON><PERSON>, ChevronDown, ChevronUp, Co<PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

// Define the structure of a tool call
export interface ToolCall {
  name: string;
  arguments: string;
}

// Props for the ToolCallDisplay component
export interface ToolCallDisplayProps {
  toolCalls: ToolCall[];
}

const ToolCallDisplay: React.FC<ToolCallDisplayProps> = ({ toolCalls }) => {
  // State to track which tool calls are expanded
  const [expandedTools, setExpandedTools] = useState<Record<number, boolean>>({});

  // Function to format arguments as JSON
  const formatArgs = (args: string | object): string => {
    try {
      // If arguments is already an object, stringify it with indentation
      if (typeof args === "object") {
        return JSON.stringify(args, null, 2);
      }

      // If arguments is a string, try to parse it as JSON and then format it
      const parsed = JSON.parse(args as string);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      // If parsing fails, return the original string as a string
      return String(args);
    }
  };

  // Toggle expanded state for a tool call
  const toggleExpand = (index: number) => {
    setExpandedTools((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  // Copy tool call arguments to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="flex flex-col space-y-4 w-full font-inter">
      {toolCalls.map((toolCall, index) => (
        <motion.div
          key={index}
          className="flex flex-col bg-melodyze-secondary/60 border border-melodyze-cyan/30 rounded-xl shadow-lg overflow-hidden backdrop-blur-xl"
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            duration: 0.4,
            type: "spring",
            stiffness: 300,
            delay: index * 0.1,
          }}
          whileHover={{ scale: 1.02, y: -2 }}
          style={{
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1)",
          }}
        >
          {/* Tool call header */}
          <motion.div
            className="flex items-center justify-between p-4 bg-melodyze-tertiary/50 cursor-pointer hover:bg-melodyze-tertiary/70 transition-all duration-300"
            onClick={() => toggleExpand(index)}
            whileHover={{ x: 2 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <div className="flex items-center gap-3">
              <motion.div
                className="bg-gradient-to-br from-melodyze-cyan to-melodyze-purple p-2 rounded-xl border border-melodyze-cyan/30"
                whileHover={{ scale: 1.2, rotate: 15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Wrench className="text-white h-4 w-4" />
              </motion.div>
              <span className="font-medium text-melodyze-cyan text-lg">{toolCall.name}</span>
            </div>
            <div className="flex items-center space-x-3">
              <motion.button
                onClick={(e) => {
                  e.stopPropagation();
                  copyToClipboard(formatArgs(toolCall.arguments));
                }}
                className="text-melodyze-cyan/70 hover:text-melodyze-cyan p-2 rounded-xl hover:bg-melodyze-cyan/10 transition-all duration-300 border border-melodyze-cyan/20"
                title="Copy to clipboard"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Copy size={16} />
              </motion.button>
              <motion.div animate={{ rotate: expandedTools[index] ? 180 : 0 }} transition={{ duration: 0.3 }}>
                {expandedTools[index] ? (
                  <ChevronUp className="text-melodyze-cyan h-5 w-5" />
                ) : (
                  <ChevronDown className="text-melodyze-cyan h-5 w-5" />
                )}
              </motion.div>
            </div>
          </motion.div>

          {/* Tool call arguments */}
          <AnimatePresence>
            {expandedTools[index] && (
              <motion.div
                className="bg-melodyze-tertiary/30 border-t border-melodyze-cyan/20 p-5 overflow-x-auto"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <motion.pre
                  className="text-sm text-gray-200 whitespace-pre-wrap font-mono bg-melodyze-secondary/40 p-4 rounded-xl border border-melodyze-cyan/20 overflow-x-auto break-words"
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  style={{
                    boxShadow: "inset 0 2px 10px rgba(0, 0, 0, 0.3)",
                  }}
                >
                  {formatArgs(toolCall.arguments)}
                </motion.pre>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      ))}
    </div>
  );
};

export default ToolCallDisplay;
