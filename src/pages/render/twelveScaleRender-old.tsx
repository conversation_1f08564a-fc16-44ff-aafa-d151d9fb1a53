"use client";
import React, { useState, useEffect } from "react";
import { Song<PERSON>cale<PERSON>eyMap, SongScaleList } from "@animesh-melodyze/ts-shared";
import ConfirmationDialog from "../../components/dialog-box/ConfirmationDialog";
import FullScreenLoader from "../../components/screens/FullScreenLoader";
import BrowseWindow from "@/components/dialog-box/BrowseWindow";
import { MeloButton } from "@animesh-melodyze/melodyze-ts-ui-modules";
import StyleAPIService from "@/utils/styleApiService";
import commons from "@/utils/Commons";
import SuccessDialog from "@/components/dialog-box/SuccessDialog";

const TwelveScaleRender: React.FC = () => {
  const [defaultKey, setDefaultKey] = useState<string>("");
  const [waitTime, setWaitTime] = useState<number>(0);
  const [saveSession, setSaveSession] = useState<boolean>(false);
  const [midiFilePath, setMidiFilePath] = useState<string>("");
  const [projectFilePath, setProjectFilePath] = useState<string>("");
  const [outputDirectory, setOutputDirectory] = useState<string>("");
  const [pitchShiftCenter, setPitchShiftCenter] = useState<number>(-5);

  const [isFormFilled, setIsFormFilled] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: () => {},
  });

  const getPitchRange = () => {
    const downShift = pitchShiftCenter;
    const upShift = pitchShiftCenter + 11;
    return { downShift, upShift };
  };

  const totalSteps = 24;
  const minPitch = -12;
  const maxPitch = 11;

  function checkFormFillStatus(): boolean {
    return !!defaultKey && !!midiFilePath && !!projectFilePath && !!outputDirectory;
  }

  const onProjectFileSelect = (filePath: string) => {
    setProjectFilePath(filePath);
  };

  const handleMidiFileSelect = async (filePath: string) => {
    setMidiFilePath(filePath);
  };

  const handleOutputDirSelect = async (dirpath: string) => {
    setOutputDirectory(dirpath);
  };

  const handleFormReset = () => {
    setDefaultKey("");
    setMidiFilePath("");
    setProjectFilePath("");
    setOutputDirectory("");
    setSaveSession(false);
    setWaitTime(0);
    setPitchShiftCenter(-5);
    setIsFormFilled(false);
  };

  const handleSubmit = async () => {
    setIsLoaderOpen(true);
    setLoaderMessage("Submitting...");
    setLoaderProgress(15);
    try {
      setLoaderProgress(50);
      const { downShift, upShift } = getPitchRange();

      const payload = {
        default_scale: defaultKey,
        down_shift: downShift,
        up_shift: upShift,
        midi_zip_path: midiFilePath,
        project_zip_path: projectFilePath,
        output_dir: outputDirectory,
        save_session: saveSession,
        wait_time: waitTime,
      };

      await StyleAPIService.twelveScaleRender(payload);

      setLoaderMessage("Final Saving...");
      setLoaderProgress(100);
      setIsLoaderOpen(false);
    } catch (error: any) {
      console.error(error);
      alert(`Error: ${error.meesage || error}`);
      setIsLoaderOpen(false);
    }
  };

  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset",
      message: "Are you sure you want to reset ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  const showSubmitConfirmation = async () => {
    setConfirmationDialogConfig({
      title: "Confirm Submission",
      message: "Are you sure you want to render this project ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        await handleSubmit();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  const handleSuccessDialogClose = () => {
    handleFormReset();
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  useEffect(() => {
    setIsFormFilled(checkFormFillStatus());
  }, [defaultKey, midiFilePath, projectFilePath, outputDirectory]);

  // --------------- RENDER ---------------
  return (
    <>
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />

      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Twelve scale rendering started successfully, Please wait while rendering."}
        onConfirm={handleSuccessDialogClose}
      />

      <p className="p-3 mt-3 font-semibold">Twelve Scale Render</p>
      <div className="w-[80%] mx-auto p-4 bg-gray-900">
        <div className="space-y-6">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label htmlFor="pitch" className="block font-medium">
                Default Key <span className="text-red-500">*</span>
              </label>
              <select
                id="default_key"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                value={defaultKey || ""}
                onChange={(e) => setDefaultKey(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select a default key
                </option>
                {SongScaleList.map((sc, i) => (
                  <option key={i} value={SongScaleKeyMap[sc]}>
                    {sc}
                  </option>
                ))}
              </select>
            </div>

            <div></div>

            <div className="flex justify-end flex-wrap gap-6">
              <div className="space-y-2 min-w-[30%]">
                <label htmlFor="wait_time" className="block font-medium whitespace-nowrap">
                  VST Loading Time (in seconds)
                </label>
                <input
                  id="wait_time"
                  type="number"
                  name="waitTime"
                  className="border border-gray-600 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200"
                  value={waitTime}
                  min={0}
                  onChange={(e) => setWaitTime(Number(e.target.value))}
                  placeholder="Enter wait time"
                />
              </div>

              <div className="space-y-2 min-w-[50px] text-center">
                <label htmlFor="save_session" className="block font-medium text-gray-200 whitespace-nowrap">
                  Save Session
                </label>
                <label className="inline-flex items-center justify-center w-full cursor-pointer">
                  <input type="checkbox" className="sr-only" checked={saveSession} onChange={(e) => setSaveSession(e.target.checked)} />
                  <div className="toggle-switch"></div>
                </label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block font-medium text-gray-200">
              Pitch Shift Range: from <span className="text-pink-400">{getPitchRange().downShift}</span> to{" "}
              <span className="text-pink-400">{getPitchRange().upShift}</span>
            </label>

            <div className="relative w-full h-6">
              <div className="absolute top-1/2 transform -translate-y-1/2 w-full h-1 bg-gray-700 rounded" />
              <div
                className="absolute top-1/2 transform -translate-y-1/2 h-1 bg-cyan-500 rounded"
                style={{
                  left: `${((getPitchRange().downShift - minPitch) / (maxPitch - minPitch)) * 100}%`,
                  width: `${((getPitchRange().upShift - getPitchRange().downShift) / (maxPitch - minPitch)) * 100}%`,
                }}
              />

              <input
                type="range"
                min={minPitch}
                max={0}
                step={1}
                value={pitchShiftCenter}
                onChange={(e) => setPitchShiftCenter(Number(e.target.value))}
                className="w-full h-6 bg-transparent appearance-none z-10 relative"
                style={{ cursor: "pointer" }}
              />
            </div>

            {/* Tick Labels */}
            <div className="flex justify-between text-xs text-gray-400">
              {Array.from({ length: totalSteps }, (_, i) => minPitch + i).map((val) => (
                <span key={val} className="w-[3%] text-center">
                  {val}
                </span>
              ))}
            </div>
          </div>

          <BrowseWindow
            label="Select Output Directory"
            isDir={true}
            onSelect={handleOutputDirSelect}
            value={outputDirectory}
            className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
          />

          <div className="grid grid-cols-2 gap-4">
            <BrowseWindow
              label="Project file (.zip)"
              extensions={["zip"]}
              onSelect={onProjectFileSelect}
              value={projectFilePath}
              className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
            />
            <BrowseWindow
              label="MIDI file (.zip)"
              extensions={["zip"]}
              onSelect={handleMidiFileSelect}
              value={midiFilePath}
              className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
            />
          </div>

          <div className="grid grid-cols-4 gap-4">
            <div className="col-span-3">
              <MeloButton
                variant="blur"
                text="Render"
                enabled={isFormFilled}
                onPressed={showSubmitConfirmation}
                height={2}
                innerPadding={"4px"}
              />
            </div>

            <div>
              <button
                onClick={showFormResetConfirmation}
                style={{ borderRadius: "10px" }}
                onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1.02)")}
                onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
                className="w-full px-4 py-3 bg-transparent text-white transition-all transform duration-300 border border-cyan-300"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
        <style jsx>{`
          .range-container {
            position: relative;
            width: 100%;
          }
          .range-track {
            position: absolute;
            height: 2px;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            background: #444;
            z-index: 0;
            border-radius: 4px;
          }
          .range-highlight {
            position: absolute;
            height: 2px;
            background: rgb(72, 214, 236);
            z-index: 1;
            border-radius: 4px;
          }
          .range-labels {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #ccc;
            margin-top: 4px;
          }

          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            height: 0;
            width: 0;
            background: transparent;
            cursor: pointer;
          }

          input[type="range"]::-moz-range-thumb {
            height: 0;
            width: 0;
            background: transparent;
            border: none;
          }
        `}</style>
      </div>
    </>
  );
};

export default TwelveScaleRender;
