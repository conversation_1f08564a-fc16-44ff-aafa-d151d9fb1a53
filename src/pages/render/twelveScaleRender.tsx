"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Clock,
  Settings,
  FileArchive,
  AlertCircle,
  CheckCircle2,
  RotateCcw,
  Sliders,
  KeyRoundIcon,
  SaveIcon,
  PlayIcon,
  ActivityIcon,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { SongScaleKeyMap, SongScaleList } from "@animesh-melodyze/ts-shared";
import ConfirmationDialog from "../../components/dialog-box/ConfirmationDialog";
import FullScreenLoader from "../../components/screens/FullScreenLoader";
import BrowseWindow from "@/components/dialog-box/BrowseWindow";
import StyleAPIService from "@/utils/styleApiService";
import commons from "@/utils/Commons";
import SuccessDialog from "@/components/dialog-box/SuccessDialog";

const TwelveScaleRender: React.FC = () => {
  const [defaultKey, setDefaultKey] = useState<string>("");
  const [waitTime, setWaitTime] = useState<number>(0);
  const [saveSession, setSaveSession] = useState<boolean>(false);
  const [midiFilePath, setMidiFilePath] = useState<string>("");
  const [projectFilePath, setProjectFilePath] = useState<string>("");
  const [outputDirectory, setOutputDirectory] = useState<string>("");
  const [pitchShiftCenter, setPitchShiftCenter] = useState<number>(-5);

  const [isFormFilled, setIsFormFilled] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: () => { },
  });

  const getPitchRange = () => {
    const downShift = pitchShiftCenter;
    const upShift = pitchShiftCenter + 11;
    return { downShift, upShift };
  };

  const totalSteps = 24;
  const minPitch = -12;
  const maxPitch = 11;

  function checkFormFillStatus(): boolean {
    return !!defaultKey && !!midiFilePath && !!projectFilePath && !!outputDirectory;
  }

  const onProjectFileSelect = (filePath: string) => {
    setProjectFilePath(filePath);
  };

  const handleMidiFileSelect = async (filePath: string) => {
    setMidiFilePath(filePath);
  };

  const handleOutputDirSelect = async (dirpath: string) => {
    setOutputDirectory(dirpath);
  };

  const handleFormReset = () => {
    setDefaultKey("");
    setMidiFilePath("");
    setProjectFilePath("");
    setOutputDirectory("");
    setSaveSession(false);
    setWaitTime(0);
    setPitchShiftCenter(-5);
    setIsFormFilled(false);
  };

  const handleSubmit = async () => {
    setIsLoaderOpen(true);
    setLoaderMessage("Submitting...");
    setLoaderProgress(15);
    try {
      setLoaderProgress(50);
      const { downShift, upShift } = getPitchRange();

      const payload = {
        default_scale: defaultKey,
        down_shift: downShift,
        up_shift: upShift,
        midi_zip_path: midiFilePath,
        project_zip_path: projectFilePath,
        output_dir: outputDirectory,
        save_session: saveSession,
        wait_time: waitTime,
      };

      await StyleAPIService.twelveScaleRender(payload);

      setLoaderMessage("Final Saving...");
      setLoaderProgress(100);
      setIsLoaderOpen(false);
    } catch (error: any) {
      console.error(error);
      alert(`Error: ${error.meesage || error}`);
      setIsLoaderOpen(false);
    }
  };

  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset",
      message: "Are you sure you want to reset ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  const showSubmitConfirmation = async () => {
    setConfirmationDialogConfig({
      title: "Confirm Submission",
      message: "Are you sure you want to render this project ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        await handleSubmit();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  const handleSuccessDialogClose = () => {
    handleFormReset();
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  useEffect(() => {
    setIsFormFilled(checkFormFillStatus());
  }, [defaultKey, midiFilePath, projectFilePath, outputDirectory]);

  // --------------- RENDER ---------------
  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-melodyze-secondary via-melodyze-tertiary to-black text-gray-100 font-inter"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />

      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />

      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Twelve scale rendering started successfully, Please wait while rendering."}
        onConfirm={handleSuccessDialogClose}
      />

      {/* Header */}
      <motion.div
        className="container mx-auto px-6 py-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="flex justify-between items-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <motion.h1
            className="text-4xl font-iceland font-bold bg-gradient-to-r from-melodyze-cyan via-melodyze-purple to-melodyze-pink bg-clip-text text-transparent flex items-center"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
              <ActivityIcon className="mr-4 text-melodyze-cyan h-8 w-8" />
            </motion.div>
            Twelve Scale Render
          </motion.h1>
        </motion.div>

        <motion.div
          className="bg-melodyze-secondary/30 backdrop-blur-xl border-2 border-melodyze-cyan/20 rounded-2xl overflow-hidden shadow-2xl relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <AnimatePresence mode="wait">
            {isFormFilled ? (
              <motion.div
                key="success"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: 180 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="absolute top-6 right-6 z-10"
              >
                <CheckCircle2 className="text-melodyze-cyan w-7 h-7" />
              </motion.div>
            ) : (
              <motion.div
                key="warning"
                initial={{ scale: 0, rotate: 180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: -180 }}
                transition={{ type: "spring", stiffness: 300 }}
                className="absolute top-6 right-6 z-10 cursor-pointer"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <AlertCircle className="text-melodyze-pink w-7 h-7" />
              </motion.div>
            )}
          </AnimatePresence>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              showSubmitConfirmation();
            }}
            className="p-8 space-y-10"
          >
            {/* Basic Configuration Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-cyan to-melodyze-purple bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
                  <Settings className="mr-3 text-melodyze-cyan h-6 w-6" />
                </motion.div>
                Basic Configuration
              </motion.h2>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.6, staggerChildren: 0.1 }}
              >
                {/* Default Key */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <KeyRoundIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="default_key" className="text-sm font-medium text-gray-300">
                      Default Key <span className="text-indigo-500">*</span>
                    </label>
                  </div>
                  <select
                    id="default_key"
                    className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                    value={defaultKey || ""}
                    onChange={(e) => setDefaultKey(e.target.value)}
                    required
                  >
                    <option value="" disabled>
                      Select a default key
                    </option>
                    {SongScaleList.map((sc, i) => (
                      <option key={i} value={SongScaleKeyMap[sc]}>
                        {sc}
                      </option>
                    ))}
                  </select>
                </div>

                {/* VST Loading Time */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Clock className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="wait_time" className="text-sm font-medium text-gray-300">
                      VST Loading Time (seconds)
                    </label>
                  </div>
                  <input
                    id="wait_time"
                    type="number"
                    name="waitTime"
                    className="w-full bg-gray-900/70 border border-gray-700 rounded-md py-2 px-3 text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
                    value={waitTime}
                    min={0}
                    onChange={(e) => setWaitTime(Number(e.target.value))}
                    onWheel={(e) => e.currentTarget.blur()}
                    placeholder="Enter wait time"
                  />
                </div>

                {/* Save Session Toggle */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <SaveIcon className="text-indigo-400 h-4 w-4" />
                    <label htmlFor="save_session" className="text-sm font-medium text-gray-300">
                      Save Session
                    </label>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={saveSession}
                        onChange={(e) => setSaveSession(e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600/80"></div>
                    </label>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Pitch Shift Range Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 15, scale: 1.1 }} transition={{ duration: 0.3 }}>
                  <Sliders className="mr-3 text-melodyze-purple h-6 w-6" />
                </motion.div>
                Pitch Shift Configuration
              </motion.h2>
              <div className="bg-gray-900/70 border border-gray-700 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-300">Pitch Shift Range</label>
                  <div className="text-sm text-gray-400">
                    From <span className="text-indigo-400 font-semibold">{getPitchRange().downShift}</span> to{" "}
                    <span className="text-indigo-400 font-semibold">{getPitchRange().upShift}</span>
                  </div>
                </div>

                <div className="relative w-full h-8">
                  <div className="absolute top-1/2 transform -translate-y-1/2 w-full h-2 bg-gray-700 rounded-full" />
                  <div
                    className="absolute top-1/2 transform -translate-y-1/2 h-2 rounded-full shadow-lg bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-600 transition-all duration-300"
                    style={{
                      left: `${((getPitchRange().downShift - minPitch) / (maxPitch - minPitch)) * 100}%`,
                      width: `${((getPitchRange().upShift - getPitchRange().downShift) / (maxPitch - minPitch)) * 100}%`,
                    }}
                  />

                  <input
                    type="range"
                    min={minPitch}
                    max={0}
                    step={1}
                    value={pitchShiftCenter}
                    onChange={(e) => setPitchShiftCenter(Number(e.target.value))}
                    className="w-full h-8 bg-transparent appearance-none z-10 relative cursor-pointer focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-full"
                  />
                </div>

                {/* Tick Labels */}
                <div className="flex justify-between text-xs text-gray-400 px-1">
                  {Array.from({ length: totalSteps }, (_, i) => minPitch + i).map((val) => (
                    <span key={val} className="text-center" style={{ width: `${100 / totalSteps}%` }}>
                      {val}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* File Upload Section */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <motion.h2
                className="text-2xl font-iceland font-bold text-white flex items-center bg-gradient-to-r from-melodyze-pink to-melodyze-indigo bg-clip-text text-transparent"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.div whileHover={{ rotate: 360, scale: 1.1 }} transition={{ duration: 0.5 }}>
                  <FileArchive className="mr-3 text-melodyze-pink h-6 w-6" />
                </motion.div>
                Project Files
              </motion.h2>

              {/* Output Directory */}
              <div className="space-y-2">
                <BrowseWindow
                  label="Output Directory"
                  isDir={true}
                  onSelect={handleOutputDirSelect}
                  value={outputDirectory}
                  className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
                />
              </div>

              {/* Project and MIDI Files */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <BrowseWindow
                    label="Project File (.zip)"
                    extensions={["zip"]}
                    onSelect={onProjectFileSelect}
                    value={projectFilePath}
                    className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
                  />
                </div>

                <div className="space-y-2">
                  <BrowseWindow
                    label="MIDI File (.zip)"
                    extensions={["zip"]}
                    onSelect={handleMidiFileSelect}
                    value={midiFilePath}
                    className="bg-gray-900/70 border-gray-700/50 hover:border-indigo-500/50 focus:border-indigo-500/50"
                  />
                </div>
              </div>
            </motion.div>

            {/* Form Actions */}
            <motion.div
              className="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-6 pt-8 border-t border-melodyze-cyan/20"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="submit"
                  disabled={!isFormFilled}
                  className="px-10 py-3 flex items-center font-inter font-semibold bg-gradient-to-r from-melodyze-pink via-melodyze-purple to-melodyze-indigo transition-all duration-500 hover:from-melodyze-pink/80 hover:via-melodyze-purple/80 hover:to-melodyze-indigo/80 shadow-lg hover:shadow-melodyze-purple/25 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="mr-3 h-5 w-5" /> Start Rendering
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  type="button"
                  onClick={showFormResetConfirmation}
                  variant="outline"
                  className="px-8 py-3 font-inter font-medium text-gray-300 border-melodyze-cyan/30 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 transition-all duration-300"
                >
                  <RotateCcw className="mr-3 h-4 w-4" /> Reset Form
                </Button>
              </motion.div>
            </motion.div>
          </form>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default TwelveScaleRender;
