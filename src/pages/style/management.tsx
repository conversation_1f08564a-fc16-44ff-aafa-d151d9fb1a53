"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Filter,
  Music,
  Clock,
  User,
  Tag,
  Volume2,
  Trash2, ChevronLeft,
  ChevronRight,
  AlertCircle,
  Loader2,
  X,
  Database,
  GaugeCircle,
  Settings2,
  KeyRoundIcon,
  SectionIcon,
  RotateCcw,
  Calendar
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import AudioPlayer from "@/components/audio-player/AudioPlayer";
import ProcessedStyleModal from "@/components/ProcessedStyleModal";
import { TimeSignKeyMap, TimeSignList } from "@animesh-melodyze/ts-shared";
import { StyleBean } from "@/models/Style";
import StyleAPIService, { styleApiClient, StyleEndpoint } from "../../utils/styleApiService";
import { MdOutlineSwipe } from "react-icons/md";
import { VscSymbolRuler } from "react-icons/vsc";

interface StyleManagementProps { }

interface DropdownOption {
  value: string;
  label: string;
}

const StyleManagement: React.FC<StyleManagementProps> = () => {
  // State for form fields
  const [timeSignature, setTimeSignature] = useState<string>("");
  const [swing, setSwing] = useState<boolean>(false);
  const [annotator, setAnnotator] = useState<string>("");
  const [genre, setGenre] = useState<string>("");
  const [pitch, setPitch] = useState<string>("");
  const [scale, setScale] = useState<string>("");
  const [tempo, setTempo] = useState<string>("");
  const [source, setSource] = useState<string>("");
  const [section, setSection] = useState<string>("");
  const [duration, setDuration] = useState<string>("");

  // State for dropdown options
  const [annotatorOptions, setAnnotatorOptions] = useState<DropdownOption[]>([]);
  const [genreOptions, setGenreOptions] = useState<DropdownOption[]>([]);
  const [pitchOptions, setPitchOptions] = useState<DropdownOption[]>([]);
  const [scaleOptions, setScaleOptions] = useState<DropdownOption[]>([]);
  const [tempoOptions, setTempoOptions] = useState<DropdownOption[]>([]);
  const [sourceOptions, setSourceOptions] = useState<DropdownOption[]>([]);
  const [sectionOptions, setSectionOptions] = useState<DropdownOption[]>([]);
  const [durationOptions, setDurationOptions] = useState<DropdownOption[]>([]);

  // New state variables for styles and pagination
  const [styles, setStyles] = useState<StyleBean[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  // Loading states
  const [isLoadingAnnotators, setIsLoadingAnnotators] = useState(false);
  const [isLoadingGenres, setIsLoadingGenres] = useState(false);
  const [isLoadingPitches, setIsLoadingPitches] = useState(false);
  const [isLoadingScales, setIsLoadingScales] = useState(false);
  const [isLoadingTempos, setIsLoadingTempos] = useState(false);
  const [isLoadingSources, setIsLoadingSources] = useState(false);
  const [isLoadingSections, setIsLoadingSections] = useState(false);
  const [isLoadingDurations, setIsLoadingDurations] = useState(false);

  const [isDeleting, setIsDeleting] = useState(false);
  const [styleToDelete, setStyleToDelete] = useState<StyleBean | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // State for ProcessedStyleModal
  const [showProcessedModal, setShowProcessedModal] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<StyleBean | null>(null);

  // Function to create filter body for API calls
  const createFilterBody = () => {
    return {
      time_signature: timeSignature,
      swing: swing,
      annotator: annotator || undefined,
      genre: genre || undefined,
      pitch: pitch || undefined,
      scale: scale || undefined,
      tempo: tempo || undefined,
      source: source || undefined,
      section: section || undefined,
      duration_in_bars: duration || undefined,
    };
  };

  // Function to fetch dropdown options
  const fetchOptions = async (
    endpoint: string,
    setOptions: (options: DropdownOption[]) => void,
    setIsLoading: (loading: boolean) => void
  ) => {
    try {
      setIsLoading(true);
      const filterBody = createFilterBody();
      const response = await styleApiClient.post(endpoint, filterBody);
      const data = response.data.data || [];
      setOptions(data.map((item: string) => ({ value: item, label: item })));
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch styles with pagination
  const fetchStyles = async () => {
    if (!timeSignature) {
      alert("Please select Time Signature first");
      return;
    }
    setIsLoading(true);
    try {
      const filterBody = createFilterBody();
      const { styles, paginated_cursor } = await StyleAPIService.getOriginalStyles(filterBody);
      setStyles(styles);
      setTotalPages(Math.max(1, Math.ceil(paginated_cursor.total / pageSize)));
    } catch (error) {
      console.error("Error fetching styles:", error);
      alert("Error fetching styles. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to fetch all dropdowns except the one being changed
  const fetchAllDropdowns = async (excludeEndpoint?: string) => {
    if (!timeSignature) return;

    const endpoints = [
      { endpoint: StyleEndpoint.GET_ANNOTATORS, setOptions: setAnnotatorOptions, setIsLoading: setIsLoadingAnnotators },
      { endpoint: StyleEndpoint.GET_GENRES, setOptions: setGenreOptions, setIsLoading: setIsLoadingGenres },
      { endpoint: StyleEndpoint.GET_PITCHES, setOptions: setPitchOptions, setIsLoading: setIsLoadingPitches },
      { endpoint: StyleEndpoint.GET_SCALES, setOptions: setScaleOptions, setIsLoading: setIsLoadingScales },
      { endpoint: StyleEndpoint.GET_TEMPOS, setOptions: setTempoOptions, setIsLoading: setIsLoadingTempos },
      { endpoint: StyleEndpoint.GET_SOURCES, setOptions: setSourceOptions, setIsLoading: setIsLoadingSources },
      { endpoint: StyleEndpoint.GET_SECTIONS, setOptions: setSectionOptions, setIsLoading: setIsLoadingSections },
      { endpoint: StyleEndpoint.GET_DURATIONS, setOptions: setDurationOptions, setIsLoading: setIsLoadingDurations },
    ];

    for (const { endpoint, setOptions, setIsLoading } of endpoints) {
      if (endpoint !== excludeEndpoint) {
        await fetchOptions(endpoint, setOptions, setIsLoading);
      }
    }
  };

  // Function to handle dropdown selection
  const handleDropdownSelect = async (value: string, setValue: (value: string) => void, endpoint: string) => {
    setValue(value);

    // Create a new filter body that includes the newly selected value
    const newFilterBody: Record<string, any> = {
      time_signature: timeSignature,
      swing: swing,
      annotator: endpoint === StyleEndpoint.GET_ANNOTATORS ? value : annotator,
      genre: endpoint === StyleEndpoint.GET_GENRES ? value : genre,
      pitch: endpoint === StyleEndpoint.GET_PITCHES ? value : pitch,
      scale: endpoint === StyleEndpoint.GET_SCALES ? value : scale,
      tempo: endpoint === StyleEndpoint.GET_TEMPOS ? value : tempo,
      source: endpoint === StyleEndpoint.GET_SOURCES ? value : source,
      section: endpoint === StyleEndpoint.GET_SECTIONS ? value : section,
      duration_in_bars: endpoint === StyleEndpoint.GET_DURATIONS ? value : duration,
    };

    // Remove keys with empty string values
    Object.keys(newFilterBody).forEach((key) => {
      if (newFilterBody[key] === "") {
        delete newFilterBody[key];
      }
    });

    // Update all dropdowns except the one being changed
    const endpoints = [
      { endpoint: StyleEndpoint.GET_ANNOTATORS, setOptions: setAnnotatorOptions, setIsLoading: setIsLoadingAnnotators },
      { endpoint: StyleEndpoint.GET_GENRES, setOptions: setGenreOptions, setIsLoading: setIsLoadingGenres },
      { endpoint: StyleEndpoint.GET_PITCHES, setOptions: setPitchOptions, setIsLoading: setIsLoadingPitches },
      { endpoint: StyleEndpoint.GET_SCALES, setOptions: setScaleOptions, setIsLoading: setIsLoadingScales },
      { endpoint: StyleEndpoint.GET_TEMPOS, setOptions: setTempoOptions, setIsLoading: setIsLoadingTempos },
      { endpoint: StyleEndpoint.GET_SOURCES, setOptions: setSourceOptions, setIsLoading: setIsLoadingSources },
      { endpoint: StyleEndpoint.GET_SECTIONS, setOptions: setSectionOptions, setIsLoading: setIsLoadingSections },
      { endpoint: StyleEndpoint.GET_DURATIONS, setOptions: setDurationOptions, setIsLoading: setIsLoadingDurations },
    ];

    for (const { endpoint: ep, setOptions, setIsLoading } of endpoints) {
      if (ep !== endpoint) {
        try {
          setIsLoading(true);
          const response = await styleApiClient.post(ep, newFilterBody);
          const data = response.data.data || [];
          setOptions(data.map((item: string) => ({ value: item, label: item })));
        } catch (error) {
          console.error(`Error fetching ${ep}:`, error);
        } finally {
          setIsLoading(false);
        }
      }
    }
  };

  // Function to handle dropdown reset
  const handleDropdownReset = async (setValue: (value: string) => void, endpoint: string) => {
    setValue("");

    // Create a new filter body that excludes the reset field
    const newFilterBody: Record<string, any> = {
      time_signature: timeSignature,
      swing: swing,
      annotator: endpoint === StyleEndpoint.GET_ANNOTATORS ? undefined : annotator,
      genre: endpoint === StyleEndpoint.GET_GENRES ? undefined : genre,
      pitch: endpoint === StyleEndpoint.GET_PITCHES ? undefined : pitch,
      scale: endpoint === StyleEndpoint.GET_SCALES ? undefined : scale,
      tempo: endpoint === StyleEndpoint.GET_TEMPOS ? undefined : tempo,
      source: endpoint === StyleEndpoint.GET_SOURCES ? undefined : source,
      section: endpoint === StyleEndpoint.GET_SECTIONS ? undefined : section,
      duration_in_bars: endpoint === StyleEndpoint.GET_DURATIONS ? undefined : duration,
    };

    // Remove keys with empty string values
    Object.keys(newFilterBody).forEach((key) => {
      if (newFilterBody[key] === "" || newFilterBody[key] === undefined) {
        delete newFilterBody[key];
      }
    });

    // Update all dropdowns except the one being reset
    const endpoints = [
      { endpoint: StyleEndpoint.GET_ANNOTATORS, setOptions: setAnnotatorOptions, setIsLoading: setIsLoadingAnnotators },
      { endpoint: StyleEndpoint.GET_GENRES, setOptions: setGenreOptions, setIsLoading: setIsLoadingGenres },
      { endpoint: StyleEndpoint.GET_PITCHES, setOptions: setPitchOptions, setIsLoading: setIsLoadingPitches },
      { endpoint: StyleEndpoint.GET_SCALES, setOptions: setScaleOptions, setIsLoading: setIsLoadingScales },
      { endpoint: StyleEndpoint.GET_TEMPOS, setOptions: setTempoOptions, setIsLoading: setIsLoadingTempos },
      { endpoint: StyleEndpoint.GET_SOURCES, setOptions: setSourceOptions, setIsLoading: setIsLoadingSources },
      { endpoint: StyleEndpoint.GET_SECTIONS, setOptions: setSectionOptions, setIsLoading: setIsLoadingSections },
      { endpoint: StyleEndpoint.GET_DURATIONS, setOptions: setDurationOptions, setIsLoading: setIsLoadingDurations },
    ];

    for (const { endpoint: ep, setOptions, setIsLoading } of endpoints) {
      if (ep !== endpoint) {
        try {
          setIsLoading(true);
          const response = await styleApiClient.post(ep, newFilterBody);
          const data = response.data.data || [];
          setOptions(data.map((item: string) => ({ value: item, label: item })));
        } catch (error) {
          console.error(`Error fetching ${ep}:`, error);
        } finally {
          setIsLoading(false);
        }
      }
    }
  };

  // Function to render a dropdown with modern design
  const renderDropdown = (
    label: string,
    value: string,
    options: DropdownOption[],
    setValue: (value: string) => void,
    endpoint: StyleEndpoint,
    isLoading: boolean,
    setOptions: (options: DropdownOption[]) => void,
    setIsLoading: (loading: boolean) => void,
    icon: React.ReactNode,
    isRequired: boolean = false,
    delay: number = 0
  ) => (
    <motion.div
      className="space-y-3"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2, delay }}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ duration: 0.2 }}>
            {icon}
          </motion.div>
          <label className="text-sm font-inter font-medium text-gray-300">
            {label} {isRequired && <span className="text-melodyze-pink">*</span>}
          </label>
        </div>
        {value && (
          <motion.button
            type="button"
            onClick={() => handleDropdownReset(setValue, endpoint)}
            className="text-xs text-gray-400 hover:text-melodyze-cyan transition-colors flex items-center font-inter"
            title={`Reset ${label}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <X className="h-3 w-3 mr-1" />
            Reset
          </motion.button>
        )}
      </div>
      <div className="relative">
        <motion.select
          className={`w-full bg-melodyze-tertiary/80 border border-melodyze-cyan/30 rounded-xl py-3 px-4 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-melodyze-cyan focus:border-melodyze-cyan transition-all duration-300 font-inter backdrop-blur-sm ${!timeSignature ? "opacity-50 cursor-not-allowed" : ""
            } ${isLoading ? "cursor-wait" : ""}`}
          value={value}
          onChange={(e) => handleDropdownSelect(e.target.value, setValue, endpoint)}
          required={isRequired}
          disabled={isLoading || !timeSignature}
          whileFocus={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <option value="" disabled>
            {isLoading ? "Loading..." : !timeSignature ? "Select time signature first" : `Select ${label.toLowerCase()}`}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </motion.select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          {isLoading ? (
            <Loader2 className="h-5 w-5 text-melodyze-cyan animate-spin" />
          ) : (
            <svg className="h-5 w-5 text-melodyze-cyan/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          )}
        </div>
      </div>
    </motion.div>
  );

  // Function to handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      // Remove automatic fetch on page change
    }
  };

  function resetAllDropdowns() {
    setAnnotatorOptions([]);
    setGenreOptions([]);
    setPitchOptions([]);
    setScaleOptions([]);
    setTempoOptions([]);
    setSourceOptions([]);
    setSectionOptions([]);
    setDurationOptions([]);
  }

  function resetAllSelections() {
    setAnnotator("");
    setGenre("");
    setPitch("");
    setScale("");
    setTempo("");
    setSource("");
    setSection("");
    setDuration("");
  }

  // Function to handle global reset
  const handleGlobalReset = () => {
    setStyles([]);
    setTimeSignature("");
    setSwing(false);
    resetAllSelections();
    resetAllDropdowns();
  };

  // Function to handle style deletion
  const handleDeleteStyle = async (style: StyleBean) => {
    setStyleToDelete(style);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!styleToDelete) return;

    setIsDeleting(true);
    try {
      await StyleAPIService.deleteStyle(styleToDelete._id);
      await fetchStyles();
      alert("Style deleted successfully");
    } catch (error) {
      console.error("Error deleting style:", error);
      alert("Error deleting style. Please try again.");
    } finally {
      setIsDeleting(false);
      setStyleToDelete(null);
      setShowDeleteConfirm(false);
    }
  };

  const cancelDelete = () => {
    setStyleToDelete(null);
    setShowDeleteConfirm(false);
  };

  // Function to handle style item click
  const handleStyleClick = (style: StyleBean) => {
    setSelectedStyle(style);
    setShowProcessedModal(true);
  };

  // Function to close processed style modal
  const closeProcessedModal = () => {
    setShowProcessedModal(false);
    setSelectedStyle(null);
  };

  // Effect to fetch styles when page changes
  useEffect(() => {
    if (currentPage > 1) {
      fetchStyles();
    }
  }, [currentPage]);

  // Effect to reset dependent dropdowns when time signature or swing changes
  useEffect(() => {
    if (timeSignature) {
      resetAllSelections();
      resetAllDropdowns();
      fetchAllDropdowns();
    }
  }, [timeSignature, swing]);

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-melodyze-secondary via-melodyze-tertiary to-black text-gray-100 font-inter"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="bg-melodyze-secondary/95 backdrop-blur-xl border border-melodyze-cyan/20 shadow-2xl">
          <motion.div initial={{ scale: 0.9, opacity: 0 }} animate={{ scale: 1, opacity: 1 }} transition={{ duration: 0.2 }}>
            <DialogHeader>
              <DialogTitle className="flex items-center text-red-400 font-inter font-semibold">
                <motion.div initial={{ rotate: 0 }} animate={{ rotate: 360 }} transition={{ duration: 0.5 }}>
                  <AlertCircle className="mr-2 h-5 w-5" />
                </motion.div>
                Confirm Deletion
              </DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p className="text-gray-300 font-inter">Are you sure you want to delete this style? This action cannot be undone.</p>
            </div>
            <DialogFooter className="gap-3">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant="outline"
                  onClick={cancelDelete}
                  disabled={isDeleting}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 font-inter"
                >
                  Cancel
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                  disabled={isDeleting}
                  className="bg-red-600 hover:bg-red-700 font-inter font-medium"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </>
                  )}
                </Button>
              </motion.div>
            </DialogFooter>
          </motion.div>
        </DialogContent>
      </Dialog>

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="flex justify-between items-center mb-8"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <motion.h1
            className="text-3xl font-iceland font-bold flex items-center bg-gradient-to-r from-melodyze-cyan via-melodyze-purple to-melodyze-pink bg-clip-text text-transparent"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div whileHover={{ rotate: 180 }} transition={{ duration: 0.3 }}>
              <Settings2 className="mr-3 text-melodyze-cyan" size={28} />
            </motion.div>
            Manage Styles
          </motion.h1>
        </motion.div>

        {/* Filter Section */}
        <motion.div
          className="bg-melodyze-secondary/40 backdrop-blur-xl border border-melodyze-cyan/20 rounded-2xl overflow-hidden shadow-2xl mb-8"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{ borderColor: "rgba(34, 211, 238, 0.4)" }}
        >
          <div className="p-6">
            <motion.h2
              className="text-xl font-inter font-semibold text-white flex items-center mb-6"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <motion.div whileHover={{ scale: 1.1 }} transition={{ duration: 0.2 }}>
                <Filter className="mr-3 text-melodyze-indigo h-6 w-6" />
              </motion.div>
              Filter Styles
            </motion.h2>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              {/* Time Signature */}
              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center space-x-2">
                  <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ duration: 0.2 }}>
                    <Clock className="text-melodyze-indigo h-5 w-5" />
                  </motion.div>
                  <label htmlFor="timeSignature" className="text-sm font-inter font-medium text-gray-300">
                    Time Signature <span className="text-melodyze-pink">*</span>
                  </label>
                </div>
                <div className="relative">
                  <motion.select
                    id="timeSignature"
                    className="w-full bg-melodyze-tertiary/80 border border-melodyze-cyan/30 rounded-xl py-3 px-4 text-sm text-gray-200 appearance-none focus:outline-none focus:ring-2 focus:ring-melodyze-cyan focus:border-melodyze-cyan transition-all duration-300 font-inter backdrop-blur-sm"
                    value={timeSignature}
                    onChange={(e) => setTimeSignature(e.target.value)}
                    required
                    whileFocus={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <option value="" disabled>
                      Select time signature
                    </option>
                    {TimeSignList.map((ts, i) => (
                      <option key={i} value={TimeSignKeyMap[ts]}>
                        {ts}
                      </option>
                    ))}
                  </motion.select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-5 w-5 text-melodyze-cyan/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </motion.div>

              {/* Swing Toggle */}
              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center space-x-2">
                  <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ duration: 0.2 }}>
                    <MdOutlineSwipe className="text-melodyze-indigo h-5 w-5" />
                  </motion.div>
                  <label className="text-sm font-inter font-medium text-gray-300">
                    Swing <span className="text-melodyze-pink">*</span>
                  </label>
                </div>
                <div className="flex items-center h-[50px]">
                  <motion.button
                    type="button"
                    onClick={() => setSwing(!swing)}
                    className={`relative inline-flex h-7 w-14 items-center rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-melodyze-cyan focus:ring-offset-2 focus:ring-offset-melodyze-secondary ${swing ? "bg-gradient-to-r from-melodyze-pink to-melodyze-purple" : "bg-gray-600"
                      }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.span
                      className="inline-block h-6 w-6 transform rounded-full bg-white shadow-lg"
                      animate={{ x: swing ? 28 : 2 }}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    />
                  </motion.button>
                  <motion.span
                    className="ml-4 text-sm font-inter text-gray-300"
                    animate={{ color: swing ? "#22d3ee" : "#9ca3af" }}
                    transition={{ duration: 0.3 }}
                  >
                    {swing ? "Yes" : "No"}
                  </motion.span>
                </div>
              </motion.div>

              {/* Genre */}
              {renderDropdown(
                "Genre",
                genre,
                genreOptions,
                setGenre,
                StyleEndpoint.GET_GENRES,
                isLoadingGenres,
                setGenreOptions,
                setIsLoadingGenres,
                <Tag className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.4
              )}

              {/* Annotator */}
              {renderDropdown(
                "Annotator",
                annotator,
                annotatorOptions,
                setAnnotator,
                StyleEndpoint.GET_ANNOTATORS,
                isLoadingAnnotators,
                setAnnotatorOptions,
                setIsLoadingAnnotators,
                <User className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.5
              )}
            </motion.div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              {/* Pitch */}
              {renderDropdown(
                "Pitch",
                pitch,
                pitchOptions,
                setPitch,
                StyleEndpoint.GET_PITCHES,
                isLoadingPitches,
                setPitchOptions,
                setIsLoadingPitches,
                <KeyRoundIcon className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.6
              )}

              {/* Scale */}
              {renderDropdown(
                "Scale",
                scale,
                scaleOptions,
                setScale,
                StyleEndpoint.GET_SCALES,
                isLoadingScales,
                setScaleOptions,
                setIsLoadingScales,
                <VscSymbolRuler className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.7
              )}

              {/* Tempo */}
              {renderDropdown(
                "Tempo",
                tempo,
                tempoOptions,
                setTempo,
                StyleEndpoint.GET_TEMPOS,
                isLoadingTempos,
                setTempoOptions,
                setIsLoadingTempos,
                <GaugeCircle className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.8
              )}

              {/* Source */}
              {renderDropdown(
                "Source",
                source,
                sourceOptions,
                setSource,
                StyleEndpoint.GET_SOURCES,
                isLoadingSources,
                setSourceOptions,
                setIsLoadingSources,
                <Database className="text-melodyze-indigo h-5 w-5" />,
                false,
                0.9
              )}
            </motion.div>

            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.4 }}
            >
              {/* Section */}
              {renderDropdown(
                "Section",
                section,
                sectionOptions,
                setSection,
                StyleEndpoint.GET_SECTIONS,
                isLoadingSections,
                setSectionOptions,
                setIsLoadingSections,
                <SectionIcon className="text-melodyze-indigo h-5 w-5" />,
                false,
                1.0
              )}

              {/* Duration in Bars */}
              {renderDropdown(
                "Duration in Bars",
                duration,
                durationOptions,
                setDuration,
                StyleEndpoint.GET_DURATIONS,
                isLoadingDurations,
                setDurationOptions,
                setIsLoadingDurations,
                <Clock className="text-melodyze-indigo h-5 w-5" />,
                false,
                1.1
              )}
            </motion.div>
          </div>

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col p-6 sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-4 pt-4 border-t border-melodyze-cyan/20"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                onClick={fetchStyles}
                disabled={isLoading || !timeSignature}
                className="px-8 py-3 flex items-center bg-gradient-to-r from-melodyze-pink via-melodyze-purple to-melodyze-indigo transition-all duration-500 hover:from-melodyze-pink/80 hover:via-melodyze-purple/80 hover:to-melodyze-indigo/80 font-inter font-medium shadow-lg hover:shadow-melodyze-cyan/25"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-5 w-5" />
                    Get Styles
                  </>
                )}
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                onClick={handleGlobalReset}
                variant="outline"
                className="text-gray-300 border-melodyze-cyan/30 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 font-inter font-medium px-6 py-3"
              >
                <RotateCcw className="mr-2 h-5 w-5" />
                Reset Filters
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Results Section */}
        <AnimatePresence>
          {styles.length > 0 && (
            <motion.div
              className="bg-melodyze-secondary/40 backdrop-blur-xl border border-melodyze-cyan/20 rounded-2xl overflow-hidden shadow-2xl"
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -30, scale: 0.95 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="p-8">
                <motion.h2
                  className="text-xl font-inter font-semibold text-white flex items-center mb-8"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <motion.div whileHover={{ scale: 1.1, rotate: 10 }} transition={{ duration: 0.2 }}>
                    <Music className="mr-3 text-melodyze-cyan h-6 w-6" />
                  </motion.div>
                  Style Results ({styles.length} found)
                </motion.h2>

                <div className="space-y-6 max-h-[700px] overflow-y-auto pr-2">
                  <AnimatePresence>
                    {styles.map((style, index) => (
                      <motion.div
                        key={style._id}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -20, scale: 0.95 }}
                        transition={{ duration: 0.4, delay: index * 0.02 }}
                        className="bg-melodyze-tertiary/60 border border-melodyze-cyan/20 rounded-2xl p-4 hover:border-melodyze-cyan/40 transition-all duration-300 backdrop-blur-sm hover:shadow-lg hover:shadow-melodyze-cyan/10 cursor-pointer"
                        whileHover={{ scale: 1.01, y: -2 }}
                        onClick={() => handleStyleClick(style)}
                      >
                        {/* Style Details Grid */}
                        <motion.div
                          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.2 }}
                        >

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <Clock className="mr-2 h-4 w-4" />
                              Time Signature
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.time_signature}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <MdOutlineSwipe className="mr-2 h-4 w-4" />
                              Swing
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.swing ? "Yes" : "No"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <Tag className="mr-2 h-4 w-4" />
                              Genre
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.genre || "N/A"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <Database className="mr-2 h-4 w-4" />
                              Source
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.source || "N/A"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <KeyRoundIcon className="mr-2 h-4 w-4" />
                              Pitch
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.pitch || "N/A"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <VscSymbolRuler className="mr-2 h-4 w-4" />
                              Scale
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.scale || "N/A"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <GaugeCircle className="mr-2 h-4 w-4" />
                              Tempo
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.tempo || "N/A"}</div>
                          </motion.div>

                          <motion.div className="space-y-2" whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <User className="mr-2 h-4 w-4" />
                              Annotator
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold">{style.annotator || "N/A"}</div>
                          </motion.div>
                        </motion.div>

                        {/* Audio Players and Actions */}
                        <motion.div
                          className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t border-melodyze-cyan/20"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: 0.3 }}
                        >
                          <motion.div
                            className="space-y-2"
                            whileHover={{ scale: 1.02 }}
                            transition={{ duration: 0.2 }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <Volume2 className="mr-2 h-4 w-4" />
                              Karaoke Audio
                            </div>
                            <AudioPlayer src={style.audio_path} />
                          </motion.div>

                          <motion.div
                            className="space-y-2"
                            whileHover={{ scale: 1.02 }}
                            transition={{ duration: 0.2 }}
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="flex items-center text-xs text-melodyze-cyan/80 font-inter font-medium">
                              <Volume2 className="mr-2 h-4 w-4" />
                              Audio with Vocals
                            </div>
                            <AudioPlayer src={style.audio_with_vocals_path} />
                          </motion.div>

                          <motion.div
                            className="flex flex-col items-end justify-end"
                            whileHover={{ scale: 1.02 }}
                            transition={{ duration: 0.2 }}
                          >
                            <div className="flex items-end text-xs text-melodyze-cyan/80 font-inter font-medium mb-1">
                              <Calendar className="mr-2 h-4 w-4" />
                              Created At
                            </div>
                            <div className="text-sm text-gray-200 font-inter font-semibold bg-melodyze-tertiary/40 rounded-lg px-3 py-2">
                              {new Date(style.created_at || 0).toLocaleString()}
                            </div>
                          </motion.div>

                          <motion.div className="flex items-end justify-end" whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteStyle(style);
                              }}
                              disabled={isDeleting}
                              variant="destructive"
                              size="sm"
                              className="bg-red-600 hover:bg-red-700 flex items-center font-inter font-medium py-2 px-4 shadow-lg hover:shadow-red-500/25"
                            >
                              {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />}
                              Delete Style
                            </Button>
                          </motion.div>
                        </motion.div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {/* Pagination */}
                <motion.div
                  className="flex justify-center items-center gap-6 mt-8 pt-6 border-t border-melodyze-cyan/20"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      variant="outline"
                      size="sm"
                      className="border-melodyze-cyan/30 text-gray-300 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 font-inter font-medium px-4 py-2"
                    >
                      <ChevronLeft className="mr-2 h-4 w-4" />
                      Previous
                    </Button>
                  </motion.div>

                  <motion.span
                    className="text-sm text-gray-300 px-6 font-inter font-medium"
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.3, delay: 0.7 }}
                  >
                    Page {currentPage} of {totalPages}
                  </motion.span>

                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      variant="outline"
                      size="sm"
                      className="border-melodyze-cyan/30 text-gray-300 hover:bg-melodyze-cyan/10 hover:border-melodyze-cyan/50 font-inter font-medium px-4 py-2"
                    >
                      Next
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Processed Style Modal */}
        <ProcessedStyleModal
          isOpen={showProcessedModal}
          onClose={closeProcessedModal}
          originalStyle={selectedStyle}
        />
      </div>
    </motion.div>
  );
};

export default StyleManagement;
