import commons from "../../utils/Commons";
import StyleAPIService from "../../utils/styleApiService";
import { SongScaleKeyMap, SongScaleList, TimeSignKeyMap, TimeSignList } from "@animesh-melodyze/ts-shared";
import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import { AiFillWarning, AiOutlineReload, AiOutlineFolderOpen, AiOutlineFileZip } from "react-icons/ai";
import { RxOpenInNewWindow } from "react-icons/rx";
import { IoMdCheckmarkCircle } from "react-icons/io";
import ConfirmationDialog from "../../components/dialog-box/ConfirmationDialog";
import SelectAudioItemDialog, { FetchStyleResult } from "../../components/dialog-box/SelectableAudioPlayer";
import SuccessDialog from "../../components/dialog-box/SuccessDialog";
import PaginatedDropdown from "../../components/PaginatedDropdown";
import FullScreenLoader from "../../components/screens/FullScreenLoader";
import { open } from "@tauri-apps/plugin-dialog";
import { AnnotatorList, SongPitchKeyMap, SongPitchList, StyleBean } from "@/models/Style";

const SectionSkeleton = () => (
  <div className="border border-gray-300 bg-gray-700/50 rounded p-6 space-y-2 mt-12 relative animate-pulse">
    <div className="flex items-center justify-between px-3 mb-6">
      <div className="h-8 bg-gray-600 rounded w-1/3"></div>
      <div className="h-10 bg-gray-600 rounded w-1/4"></div>
    </div>
    <div className="flex space-x-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="w-1/4 h-10 bg-gray-600 rounded"></div>
      ))}
    </div>
    <div className="grid grid-cols-4 gap-3 w-full">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-10 bg-gray-600 rounded"></div>
      ))}
    </div>
    <div className="grid grid-cols-4 gap-3 mt-3 w-full">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-10 bg-gray-600 rounded"></div>
      ))}
    </div>
  </div>
);
interface StyleTransferProps {}

interface StyleSection {
  styleId?: string;
  slNo: number;
  midiSection: string;
  isBlank: boolean;

  startBar: number | "";
  startBeat: number | "";
  endBar: number | "";
  endBeat: number | "";

  genre?: string;
  source?: string;
  scale?: string;
  pitch?: string;
  durationBar?: string;
  tempo?: string;
  sectionName?: string;
  annotator?: string;
}

interface IStyleTransferForm {
  inputSongName: string;
  inputTimeSignature: string;
  inputSwing: boolean;
  inputGenre: string;
  inputPitch: string;
  inputAnnotator: string;
  inputScale: string;
  inputTempo: string;
  inputMidiPath: string;
  outputDir: string;
  sections: StyleSection[];
}

const StyleTransferPageOld: React.FC<StyleTransferProps> = () => {
  const [form, setForm] = useState<IStyleTransferForm>({
    inputSongName: "",
    inputTimeSignature: "",
    inputSwing: false,
    inputGenre: "",
    inputScale: "",
    inputTempo: "",
    inputAnnotator: "",
    inputPitch: "",
    inputMidiPath: "",
    outputDir: "",
    sections: [],
  });

  const [genreList, setGenreList] = useState<string[]>([]);
  const [isMainInputFormFilled, setIsMainInputFormFilled] = useState<boolean>(false);

  const [isMidiLoading, setIsMidiLoading] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: () => {},
  });

  const getMainInputFillStatus = (form: IStyleTransferForm) => {
    return (
      !!form.inputSongName &&
      !!form.inputMidiPath &&
      !!form.inputTimeSignature &&
      form.inputSwing !== null &&
      !!form.inputGenre &&
      !!form.inputScale &&
      !!form.inputTempo &&
      !!form.inputAnnotator &&
      !!form.inputPitch &&
      !!form.outputDir &&
      Number(form.inputTempo) >= 60 &&
      Number(form.inputTempo) <= 300
    );
  };

  const resetSectionFields = (form: IStyleTransferForm, sectionIndex?: number) => {
    const resetFields: Partial<StyleSection> = {
      styleId: undefined,
      genre: undefined,
      scale: undefined,
      tempo: undefined,
      sectionName: undefined,
      source: undefined,
      pitch: undefined,
      durationBar: undefined,
      annotator: undefined,
    };

    if (typeof sectionIndex === "number") {
      return {
        ...form,
        sections: form.sections.map((section, i) => (i === sectionIndex ? { ...section, ...resetFields } : section)),
      };
    } else {
      return {
        ...form,
        sections: form.sections.map((section) => ({
          ...section,
          ...resetFields,
        })),
      };
    }
  };

  const handleMainFormInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    let updatedForm = { ...form, [name]: value.trim() };

    // If MIDI file is present and inputTimeSignature or inputTempo is updated, reset MIDI and sections
    if (form.inputMidiPath && (name === "inputTimeSignature" || name === "inputTempo")) {
      updatedForm.inputMidiPath = "";
      updatedForm.sections = [];
    }

    if (name === "inputTimeSignature") {
      updatedForm = resetSectionFields(updatedForm);
    }
    setForm(updatedForm);
    setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
  };

  const handleOutputDirectoryChange = async () => {
    try {
      const dirPath = await open({
        directory: true,
        multiple: false,
      });

      if (typeof dirPath === "string") {
        let updatedForm = { ...form, outputDir: dirPath };
        setForm(updatedForm);
        setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
      }
    } catch (err) {
      console.error("Error selecting directory:", err);
    }
  };

  const handleMidiUpload = async () => {
    const filePath = await open({
      directory: false,
      multiple: false,
      filters: [{ name: "ZIP Files", extensions: ["zip"] }],
    });

    if (!filePath) return;
    if (!filePath.endsWith(".zip")) {
      alert("Please upload a valid Midi ZIP file.");
      return;
    }
    setIsMidiLoading(true);
    try {
      const { sections } = await StyleAPIService.getMidiInfo({
        midi_path: filePath,
        time_signature: form.inputTimeSignature,
        tempo: form.inputTempo,
      });

      if (sections.length < 1) {
        alert("No Markers found in MIDI, Please select a MIDI with marker.");
        setIsMidiLoading(false);
        return;
      }
      let updatedForm = {
        ...form,
        inputMidiPath: filePath,
        sections: sections.map((section: any, index: number) => ({
          slNo: index + 1,
          isBlank: false,
          midiSection: section.name,
          startBar: section.start_bar || "",
          startBeat: section.start_beat || "",
          endBar: section.end_bar || "",
          endBeat: section.end_beat || "",
        })),
      };

      setForm(updatedForm);
      await commons.sleep(200);
      setIsMainInputFormFilled(getMainInputFillStatus(updatedForm));
    } catch (error) {
      console.error(error);
      alert("Failed to upload MIDI file");
    }
    setIsMidiLoading(false);
  };

  const handleSwingToggle = (e: ChangeEvent<HTMLInputElement>) => {
    let updatedForm = { ...form, inputSwing: e.target.checked };
    updatedForm = resetSectionFields(updatedForm);
    setForm(updatedForm);
  };

  const handleSectionItemSelect = (item: string | null, sectionIndex: number, key: string) => {
    const itemValue = item?.trim() ?? undefined;
    const updatedForm = {
      ...form,
      sections: form.sections.map((section, i) =>
        i === sectionIndex
          ? {
              ...section,
              [key]: itemValue,
              styleId: itemValue ? section.styleId : undefined,
            }
          : section
      ),
    };
    setForm(updatedForm);
  };

  const handleStyleAudioItemSelect = (item: StyleBean, sectionIndex: number) => {
    const updatedForm = {
      ...form,
      projectRefId: item.project_ref_id,
      sections: form.sections.map((section, i) =>
        i === sectionIndex
          ? {
              ...section,
              genre: item.genre,
              scale: item.scale,
              tempo: item.tempo,
              sectionName: item.section,
              source: item.source,
              styleId: item._id,
              pitch: item.pitch,
              durationBar: item.duration_in_bars,
              annotator: item.annotator,
            }
          : section
      ),
    };
    setForm(updatedForm);
  };

  const handleIsBlankToggle = (isBlank: boolean, index: number) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.map((section, i) =>
        i === index
          ? {
              ...section,
              isBlank: isBlank,
              styleId: undefined,
              genre: undefined,
              source: undefined,
              scale: undefined,
              tempo: undefined,
              sectionName: undefined,
              pitch: undefined,
              durationBar: undefined,
              annotator: undefined,
            }
          : section
      ),
    }));
  };

  function createFilterBody(key: string, index: number) {
    let filter: any = {
      time_signature: form.inputTimeSignature,
      swing: form.inputSwing,
      genre: form.sections[index].genre,
      scale: form.sections[index].scale,
      tempo: form.sections[index].tempo,
      section: form.sections[index].sectionName,
      source: form.sections[index].source,
      pitch: form.sections[index].pitch,
      annotator: form.sections[index].annotator,
      duration_in_bars: form.sections[index].durationBar,
    };
    commons.removeFalsyProps(filter);
    delete filter[key];
    return filter;
  }

  const getSectionGenres = async (sectionIndex: number) => {
    const data = await StyleAPIService.getGenres(createFilterBody("genre", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionScales = async (sectionIndex: number) => {
    const data = await StyleAPIService.getScales(createFilterBody("scale", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionPitches = async (sectionIndex: number) => {
    const data = await StyleAPIService.getPitches(createFilterBody("pitch", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionDurationBars = async (sectionIndex: number) => {
    const data = await StyleAPIService.getDurations(createFilterBody("duration_in_bars", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionNames = async (sectionIndex: number) => {
    const data = await StyleAPIService.getSections(createFilterBody("section", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionSources = async (sectionIndex: number) => {
    const data = await StyleAPIService.getSources(createFilterBody("source", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionTempos = async (sectionIndex: number) => {
    const data = await StyleAPIService.getTempos(createFilterBody("tempo", sectionIndex));
    return { data, hasMore: false };
  };
  const getSectionAnnotators = async (sectionIndex: number) => {
    const data = await StyleAPIService.getAnnotators(createFilterBody("annotator", sectionIndex));
    return { data, hasMore: false };
  };

  const getStyles = async (page: number, pageSize: number, sectionIndex: number) => {
    const { styles, paginated_cursor } = await StyleAPIService.getStyles(createFilterBody("", sectionIndex), { page, pageSize });
    return { data: styles, hasMore: paginated_cursor["has_next"] || false };
  };

  // --------------------------------------
  // Submit entire form
  // --------------------------------------

  const handleFormReset = () => {
    setForm({
      inputSongName: "",
      inputTimeSignature: "",
      inputSwing: false,
      inputGenre: "",
      inputScale: "",
      inputTempo: "",
      inputMidiPath: "",
      inputAnnotator: "",
      inputPitch: "",
      outputDir: "",
      sections: [],
    });
    setIsMainInputFormFilled(false);
  };

  async function handleSubmit(e: FormEvent) {
    e.preventDefault();
    setIsLoaderOpen(true);
    setLoaderMessage("Transferring style...");
    setLoaderProgress(10);
    try {
      const finalData = {
        time_signature: form.inputTimeSignature,
        swing: form.inputSwing,
        genre: form.inputGenre,
        scale: form.inputScale,
        tempo: form.inputTempo,
        song_name: form.inputSongName,
        input_midi_zip_path: form.inputMidiPath,
        output_directory: form.outputDir,
        annotator: form.inputAnnotator,
        pitch: form.inputPitch,
        sections: form.sections.map((item) => {
          return {
            input_midi_section: item.midiSection,
            genre: item.genre,
            source: item.source,
            section: item.sectionName,
            style_id: item.styleId,
            is_blank: item.isBlank,
            sl_no: item.slNo,
            start_bar: item.startBar,
            start_beat: item.startBeat,
            end_bar: item.endBar,
            end_beat: item.endBeat,
          };
        }),
      };
      setLoaderProgress(30);
      await StyleAPIService.styleTransfer(finalData);
      setLoaderProgress(100);
      setLoaderMessage("Style transfer complete!");
      setShowSuccessDialog(true);
    } catch (error: any) {
      console.error(error);
      alert(`Error submitting style transfer form: ${error.message || error}`);
    } finally {
      setIsLoaderOpen(false);
    }
  }

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset",
      message: "Are you sure you want to reset this style ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  useEffect(() => {
    const fetchGenreList = async () => {
      try {
        const genres: string[] = await StyleAPIService.getConfigGenres();
        setGenreList(genres);
      } catch (error: any) {
        alert(`Error fetching genres: ${error.message}`);
      }
    };
    fetchGenreList();
  }, []);

  // Render
  // --------------------------------------
  return (
    <>
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />
      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Your style has been uploaded successfully."}
        onConfirm={handleSuccessDialogClose}
      />
      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />
      <p className="p-3 mt-3 font-semibold">Transfer Style</p>

      <div className="w-[80%] mx-auto rounded-md p-4 bg-gray-900 relative">
        {isMainInputFormFilled ? (
          <IoMdCheckmarkCircle className="absolute top-1 right-2 text-green-600 w-7 h-7" />
        ) : (
          <AiFillWarning
            title="Warning! Form contains error or incomplete."
            className="absolute top-1 right-2 text-yellow-500 w-6 h-6 cursor-pointer"
          />
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* ----------  Top Level Main Form---------- */}
          <div className="w-full">
            {/* Row 1: Time Signature and Swing */}
            <div className="flex flex-col md:flex-row gap-4 mb-3">
              {/* Time Signature */}
              <div className="w-full md:w-2/4 space-y-2">
                <label htmlFor="timeSignature" className="block mb-1 font-medium text-gray-200">
                  Time Signature <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <select
                    id="timeSignature"
                    name="inputTimeSignature"
                    className={`border ${
                      form.inputTimeSignature ? "border-green-500" : "border-gray-700"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none`}
                    value={form.inputTimeSignature || ""}
                    onChange={handleMainFormInputChange}
                    required
                  >
                    <option value="" disabled>
                      Select time signature
                    </option>
                    {TimeSignList.map((ts, i) => (
                      <option key={i} value={TimeSignKeyMap[ts]}>
                        {ts}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Tempo */}
              <div className="w-full md:w-2/4 space-y-2">
                <label htmlFor="tempo" className="block mb-1 font-medium text-gray-200">
                  Tempo <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <input
                    id="tempo"
                    type="number"
                    name="inputTempo"
                    className={`border ${
                      form.inputTempo ? "border-green-500" : "border-gray-600"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200`}
                    min={60}
                    max={300}
                    value={form.inputTempo}
                    onChange={handleMainFormInputChange}
                    placeholder="Enter tempo"
                    required
                  />
                </div>
              </div>

              {/* Swing */}
              <div className="w-full md:w-1/4 space-y-2 flex justify-center items-center">
                <div className="w-full">
                  <label htmlFor="swing" className="block mb-1 font-medium text-gray-200 text-center">
                    Swing
                  </label>
                  <label className="inline-flex items-center cursor-pointer justify-center w-full">
                    <input type="checkbox" className="sr-only" checked={form.inputSwing} onChange={handleSwingToggle} />
                    <div className="toggle-switch"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* Row 2: Genre, Scale, Key, Tempo */}
            <div className="flex flex-col md:flex-row gap-4 mb-3">
              {/* Genre */}
              <div className="w-full md:w-1/3 space-y-2">
                <label htmlFor="genre" className="block mb-1 font-medium text-gray-200">
                  Genre <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <select
                    id="genre"
                    className={`border ${
                      form.inputGenre ? "border-green-500" : "border-gray-700"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none`}
                    defaultValue={form.inputGenre || ""}
                    onChange={handleMainFormInputChange}
                    name="inputGenre"
                    required
                  >
                    <option value="" disabled>
                      Select a genre
                    </option>
                    {genreList.map((val, index) => (
                      <option key={index} value={val}>
                        {val}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Key */}
              <div className="w-full md:w-1/3 space-y-2">
                <label htmlFor="pitch" className="block mb-1 font-medium text-gray-200">
                  Key <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <select
                    id="pitch"
                    name="inputPitch"
                    className={`border ${
                      form.inputPitch ? "border-green-500" : "border-gray-700"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none`}
                    value={form.inputPitch}
                    onChange={handleMainFormInputChange}
                    required
                  >
                    <option value="" disabled>
                      Select a key
                    </option>
                    {SongScaleList.map((sc, i) => (
                      <option key={i} value={SongScaleKeyMap[sc]}>
                        {sc}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Scale */}
              <div className="w-full md:w-1/3 space-y-2">
                <label htmlFor="scale" className="block mb-1 font-medium text-gray-200">
                  Scale <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <select
                    id="scale"
                    name="inputScale"
                    className={`border ${
                      form.inputScale ? "border-green-500" : "border-gray-700"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none`}
                    value={form.inputScale || ""}
                    onChange={handleMainFormInputChange}
                    required
                  >
                    <option value="" disabled>
                      Select a scale
                    </option>
                    {SongPitchList.map((p, i) => (
                      <option key={i} value={SongPitchKeyMap[p]}>
                        {p}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Row 3: Song Name, Annotator */}
            <div className="flex flex-col md:flex-row gap-4 mb-3">
              {/* Song Name */}
              <div className="w-full md:w-2/3 space-y-2">
                <label className="block mb-1 font-medium text-gray-200">
                  Song Name <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <input
                    type="text"
                    name="inputSongName"
                    className={`border ${
                      form.inputSongName ? "border-green-500" : "border-gray-600"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200`}
                    value={form.inputSongName}
                    onChange={handleMainFormInputChange}
                    placeholder="Enter song name"
                    required
                  />
                </div>
              </div>

              {/* Annotator */}
              <div className="w-full md:w-1/3 space-y-2">
                <label htmlFor="annotator" className="block mb-1 font-medium text-gray-200">
                  Annotator <span className="text-red-500">*</span>
                </label>
                <div className="relative flex items-center">
                  <select
                    id="annotator"
                    name="inputAnnotator"
                    className={`border ${
                      form.inputAnnotator ? "border-green-500" : "border-gray-700"
                    } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none`}
                    value={form.inputAnnotator}
                    onChange={handleMainFormInputChange}
                    required
                  >
                    <option value="" disabled>
                      Select annotator
                    </option>
                    {AnnotatorList.map((name, i) => (
                      <option key={i} value={name}>
                        {name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Row 4: Upload MIDI and Output Directory */}
            <div className="flex flex-col md:flex-row gap-4">
              {/* Output Directory */}
              <div className="w-full md:w-1/2 space-y-2">
                <label htmlFor="outputDirectory" className="block font-medium text-gray-200">
                  Output Directory <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={handleOutputDirectoryChange}
                  className={`border ${
                    form.outputDir ? "border-green-500" : "border-gray-600"
                  } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate`}
                >
                  <span className="flex items-center justify-between w-full">
                    <span className="flex items-center gap-2">
                      <AiOutlineFolderOpen className="inline-block" fontSize={18} />
                      {form.outputDir ? form.outputDir : "Select Output Folder"}
                    </span>
                    <RxOpenInNewWindow className="ml-2 text-xl" />
                  </span>
                </button>
              </div>

              {/* Upload MIDI */}
              <div className="w-full md:w-1/2 space-y-2">
                <label className="block font-medium text-gray-200">
                  MIDI File (.zip)<span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={handleMidiUpload}
                  className={`border ${
                    form.inputMidiPath ? "border-green-500" : "border-gray-600"
                  } bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate${
                    !form.inputTimeSignature || !form.inputTempo ? " opacity-50 cursor-not-allowed" : ""
                  }`}
                  disabled={!form.inputTimeSignature || !form.inputTempo}
                >
                  <span className="flex items-center justify-between w-full">
                    <span className="flex items-center gap-2">
                      <AiOutlineFileZip className="inline-block" fontSize={16} />
                      {form.inputMidiPath ? form.inputMidiPath : "Browse a file"}
                    </span>
                    <RxOpenInNewWindow className="ml-2 text-xl" />
                  </span>
                </button>
              </div>
            </div>
          </div>

          {/* Top-level main form ends here */}

          {/* ---------- Dynamic Child Sections Starts here---------- */}

          <div className="space-y-2" style={{ maxHeight: "700px", overflowY: "auto" }}>
            {isMidiLoading && Array.from({ length: 3 }).map((_, index) => <SectionSkeleton key={index} />)}

            {form.sections &&
              form.sections.map((section, index) => {
                const fetchSectionGenres = async (page: number, pageSize: number) => {
                  return getSectionGenres(index);
                };
                const fetchSectionScales = async (page: number, pageSize: number) => {
                  return getSectionScales(index);
                };
                const fetchSectionPitches = async (page: number, pageSize: number) => {
                  return getSectionPitches(index);
                };
                const fetchDurationBars = async (page: number, pageSize: number) => {
                  return getSectionDurationBars(index);
                };
                const fetchSectionNames = async (page: number, pageSize: number) => {
                  return getSectionNames(index);
                };
                const fetchSectionSources = async (page: number, pageSize: number) => {
                  return getSectionSources(index);
                };
                const fetchSectionTempos = async (page: number, pageSize: number) => {
                  return getSectionTempos(index);
                };
                const fetchSectionAnnotators = async (page: number, pageSize: number) => {
                  return getSectionAnnotators(index);
                };

                const onGenreSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "genre");
                };
                const onScaleSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "scale");
                };
                const onPitchSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "pitch");
                };
                const onDurationBarSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "durationBar");
                };
                const onTempoSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "tempo");
                };
                const onSourceSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "source");
                };
                const onSectionNameSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "sectionName");
                };
                const onAnnotatorSelect = (item: string | null) => {
                  handleSectionItemSelect(item, index, "annotator");
                };

                async function fetchStyleAudioItems(page: number, pageSize: number): Promise<FetchStyleResult> {
                  return getStyles(page, pageSize, index);
                }

                const onStyleAudioItemSelect = (item: StyleBean) => {
                  handleStyleAudioItemSelect(item, index);
                };

                return (
                  <div key={index} className="border border-gray-300 bg-gray-700/50 rounded p-6 space-y-2 mt-12 relative">
                    <div className="flex items-center justify-between px-3 mb-6">
                      <div
                        className="bg-gradient-to-r from-teal-500 via-cyan-500 to-blue-500 text-white px-3 py-2 rounded inline-block shadow-lg dark:from-teal-600 dark:via-cyan-700 dark:to-blue-600 dark:opacity-80 backdrop-blur-sm"
                        style={{
                          boxShadow: "2px 6px 7px rgba(9, 9, 9, 0.68)",
                          transition: "transform 0.2s ease-in-out",
                        }}
                      >
                        {section.slNo}. {section.midiSection || "Unknown Name"}
                      </div>

                      {!section.isBlank ? (
                        <div>
                          <SelectAudioItemDialog
                            fetchData={fetchStyleAudioItems}
                            onSelect={onStyleAudioItemSelect}
                            pageSize={10}
                            openButtonLabel={section.styleId ? "Style Selected" : "Listen and select style"}
                            title="Listen and Select Style"
                            appliedFilter={createFilterBody("", index)}
                            warning={!section.styleId}
                          />
                        </div>
                      ) : (
                        <IoMdCheckmarkCircle className="absolute top-1 right-2 text-green-600 w-5 h-5" />
                      )}

                      {!section.isBlank && (
                        <button
                          type="button"
                          style={{
                            boxShadow: "3px 4px 6px rgba(0, 0, 0, 0.69)",
                            transition: "transform 0.2s ease-in-out",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            fontSize: "14px",
                          }}
                          onClick={() => handleIsBlankToggle(false, index)}
                          className="font-semibold px-3 py-3 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 disabled:bg-gray-500 disabled:cursor-not-allowed"
                          disabled={section.isBlank}
                        >
                          <AiOutlineReload className="mr-2" />
                          Reset Section
                        </button>
                      )}

                      {/* Blank Section bg-pink-700/80*/}
                      <div
                        className="flex items-center px-4 py-3 border border-gray-600 rounded-lg bg-gradient-to-b from-gray-800 to-gray-900 shadow-inner transition-all hover:shadow-lg hover:from-gray-700 hover:to-gray-800 backdrop-blur-md text-gray-300"
                        style={{
                          boxShadow: "3px 4px 6px rgba(0, 0, 0, 0.69)",
                          transition: "transform 0.2s ease-in-out",
                        }}
                      >
                        <label className="font-semibold mr-3 text-sm tracking-wide">Make it as blank section</label>
                        <label className="inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only"
                            checked={section.isBlank}
                            onChange={(e) => handleIsBlankToggle(e.target.checked, index)}
                          />
                          <div
                            className={`w-10 h-5 rounded-full relative transition-all duration-300 ${
                              section.isBlank ? "bg-yellow-600" : "bg-gray-500 "
                            }`}
                          >
                            <div
                              className={`absolute top-0.5 left-0.5 w-4 h-4 bg-gradient-to-r from-gray-700 to-gray-800 rounded-full shadow-md transition-all ${
                                section.isBlank ? "translate-x-5 bg-gradient-to-r from-gray-700 to-gray-800" : ""
                              }`}
                            ></div>
                          </div>
                        </label>
                      </div>
                    </div>

                    {/* Start Bar */}
                    <div className="flex space-x-4">
                      <div className="w-1/4 space-y-1">
                        <label className="block mb-1 font-medium">Start Bar</label>
                        <input
                          type="number"
                          name="startBar"
                          className="rounded-md p-2 text-sm w-full text-gray-800 bg-gray-300/90"
                          value={section.startBar}
                          disabled
                        />
                      </div>

                      {/* Start Beat */}
                      <div className="w-1/4 space-y-1">
                        <label className="block mb-1 font-medium">Start Beat</label>
                        <input
                          type="number"
                          name="startBeat"
                          className="rounded-md p-2 text-sm w-full text-gray-800 bg-gray-300/90"
                          value={section.startBeat}
                          disabled
                        />
                      </div>

                      <div className="w-1/10 space-y-1">
                        <label className="block mb-1 font-medium"></label>
                        <div>
                          <p className="mt-9 font-bold"> : </p>
                        </div>
                      </div>

                      {/* End Bar */}
                      <div className="w-1/4 space-y-1">
                        <label className="block mb-1 font-medium">End Bar</label>
                        <input
                          type="number"
                          name="endBar"
                          className="rounded-md p-2 text-sm w-full text-gray-800 bg-gray-300/90"
                          value={section.endBar}
                          disabled
                        />
                      </div>

                      {/* End Beat */}
                      <div className="w-1/4 space-y-1">
                        <label className="block mb-1 font-medium">End Beat</label>
                        <input
                          type="number"
                          name="endBeat"
                          className="rounded-md p-2 text-sm w-full text-gray-800 bg-gray-300/90"
                          value={section.endBeat}
                          disabled
                        />
                      </div>
                    </div>

                    <div style={{ height: "0.1px" }}></div>

                    {/* Row 3: genre, section, source, scale, tempoSection */}
                    {!section.isBlank && (
                      <div className="w-full">
                        <div className="grid grid-cols-4 gap-3 w-full">
                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Annotator <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.annotator}
                              fetchData={fetchSectionAnnotators}
                              onSelect={onAnnotatorSelect}
                              placeholder="Select an Annotator"
                            />
                          </div>

                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Genre <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.genre}
                              fetchData={fetchSectionGenres}
                              onSelect={onGenreSelect}
                              placeholder="Select a genre"
                            />
                          </div>

                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Section <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.sectionName}
                              fetchData={fetchSectionNames}
                              onSelect={onSectionNameSelect}
                              placeholder="Select a Section"
                            />
                          </div>
                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Source <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.source}
                              fetchData={fetchSectionSources}
                              onSelect={onSourceSelect}
                              placeholder="Select a source"
                            />
                          </div>
                        </div>

                        {/* Second row - 3 equal columns */}
                        <div className="grid grid-cols-4 gap-3 mt-3 w-full">
                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Key <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.pitch}
                              fetchData={fetchSectionPitches}
                              onSelect={onPitchSelect}
                              placeholder="Select a key"
                            />
                          </div>

                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Scale <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.scale}
                              fetchData={fetchSectionScales}
                              onSelect={onScaleSelect}
                              placeholder="Select a scale"
                            />
                          </div>

                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Tempo <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.tempo}
                              fetchData={fetchSectionTempos}
                              onSelect={onTempoSelect}
                              placeholder="Select a tempo"
                            />
                          </div>

                          <div className="space-y-1">
                            <label className="block mb-1 font-medium">
                              Duration in Bars <span className="text-red-500">*</span>
                            </label>
                            <PaginatedDropdown
                              value={section.durationBar}
                              fetchData={fetchDurationBars}
                              onSelect={onDurationBarSelect}
                              placeholder="Select a Duration"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
          {/* ---------- Dynamic Child Sections Ends here---------- */}

          {/* ---------- Buttons: Add Section & Submit ---------- */}
          <div className="flex space-x-4">
            <button
              type="submit"
              className={`w-3/4 py-3 text-white font-semibold rounded-md ${
                isMainInputFormFilled && form.sections.length > 0 && form.sections.every((e) => e.isBlank || !!e.styleId)
                  ? "bg-pink-700 hover:bg-pink-800 transition duration-300"
                  : "cursor-not-allowed pointer-events-none bg-pink-500 opacity-50"
              }`}
            >
              Final Submit
            </button>

            <button
              type="reset"
              className="w-1/4 py-3 bg-gray-600 text-white font-semibold rounded-md hover:bg-gray-700 transition duration-300"
              onClick={(e) => {
                e.preventDefault();
                showFormResetConfirmation();
              }}
            >
              Reset
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default StyleTransferPageOld;
