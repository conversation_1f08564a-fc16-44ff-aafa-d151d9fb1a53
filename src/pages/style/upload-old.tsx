"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>eyMap, SongScaleList, TimeSignKeyMap, TimeSignList } from "@animesh-melodyze/ts-shared";
import commons from "../../utils/Commons";
import ConfirmationDialog from "../../components/dialog-box/ConfirmationDialog";
import SuccessDialog from "../../components/dialog-box/SuccessDialog";
import FullScreenLoader from "../../components/screens/FullScreenLoader";
import { open } from "@tauri-apps/plugin-dialog";
import { AnnotatorList } from "@/models/Style";
import StyleAPIService from "@/utils/styleApiService";
import { AiOutlineFolderOpen, AiOutlineFileZip, AiOutlineFileAdd } from "react-icons/ai";
import { RxOpenInNewWindow } from "react-icons/rx";
import { readFile } from "@tauri-apps/plugin-fs";
import AudioPlayer from "@/components/audio-player/AudioPlayer";
interface StyleUploadProps {}

const StyleUpload: React.FC<StyleUploadProps> = () => {
  const [time_signature, setTimeSignature] = useState<string>("");
  const [swing, setSwing] = useState<boolean>(false);
  const [genre, setGenre] = useState<string>("");
  const [source, setSource] = useState<string>("");
  const [pitch, setPitch] = useState<string>("");
  const [scale, setScale] = useState<string>("");
  const [tempo, setTempo] = useState<string>("");
  const [annotator, setAnnotator] = useState<string>("");
  const [genreList, setGenreList] = useState<string[]>([]);
  const [audioFilePath, setAudioFilePath] = useState<string>("");
  const [audioWithVocalsFilePath, setAudioWithVocalsFilePath] = useState<string>("");
  const [midiFilePath, setMidiFilePath] = useState<string>("");
  const [projectFilePath, setProjectFilePath] = useState<string>("");
  const [audioSrc, setAudioSrc] = useState<string>("");
  const [audioWithVocalsSrc, setAudioWithVocalsSrc] = useState<string>("");
  const [outputDirectory, setOutputDirectory] = useState<string>("");

  const [isFormFilled, setIsFormFilled] = useState<boolean>(false);
  const [isLoaderOpen, setIsLoaderOpen] = useState<boolean>(false);
  const [loaderProgress, setLoaderProgress] = useState<number>(0);
  const [loaderMessage, setLoaderMessage] = useState<string>("");
  const [showSuccessDialog, setShowSuccessDialog] = useState<boolean>(false);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [confirmationDialogConfig, setConfirmationDialogConfig] = useState({
    title: "",
    message: "",
    onConfirm: () => {},
  });

  function checkFormFillStatus(): boolean {
    return (
      !!time_signature &&
      !!genre &&
      !!source &&
      !!scale &&
      !!pitch &&
      !!tempo &&
      !!annotator &&
      !!audioFilePath &&
      !!audioWithVocalsFilePath &&
      !!midiFilePath &&
      !!projectFilePath &&
      !!outputDirectory
    );
  }

  // Handle file selection for audio
  const handleProjectSelect = async () => {
    try {
      const selected = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "ZIP Files", extensions: ["zip"] }],
      });
      if (typeof selected === "string" && selected.endsWith(".zip")) {
        setProjectFilePath(selected);
      } else if (selected) {
        alert("Please upload a valid ZIP file.");
      }
    } catch (err) {
      console.error("Error selecting project file:", err);
    }
  };

  const handleMidiSelect = async () => {
    try {
      const selected = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "ZIP Files", extensions: ["zip"] }],
      });
      if (typeof selected === "string" && selected.endsWith(".zip")) {
        setMidiFilePath(selected);
      } else if (selected) {
        alert("Please upload a valid MIDI ZIP file.");
      }
    } catch (err) {
      console.error("Error selecting MIDI file:", err);
    }
  };

  const handleAudioSelect = async () => {
    try {
      const selected = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "Mp3 Files", extensions: ["mp3"] }],
      });
      if (typeof selected === "string" && selected.endsWith(".mp3")) {
        setAudioFilePath(selected);
        const audioData = await readFile(selected);
        const buffer = new ArrayBuffer(audioData.byteLength);
        const view = new Uint8Array(buffer);
        view.set(audioData);
        // Now create Blob from the clean Uint8Array
        const blob = new Blob([view], { type: "audio/mpeg" });
        const url = URL.createObjectURL(blob);
        setAudioSrc(url);
      } else if (selected) {
        alert("Please upload a valid MP3 file.");
      }
    } catch (err) {
      console.error("Error selecting audio file:", err);
    }
  };

  const handleAudioWithVocalsSelect = async () => {
    try {
      const selected = await open({
        directory: false,
        multiple: false,
        filters: [{ name: "Mp3 Files", extensions: ["mp3"] }],
      });
      if (typeof selected === "string" && selected.endsWith(".mp3")) {
        setAudioWithVocalsFilePath(selected);
        const audioData = await readFile(selected);
        const buffer = new ArrayBuffer(audioData.byteLength);
        const view = new Uint8Array(buffer);
        view.set(audioData);
        // Now create Blob from the clean Uint8Array
        const blob = new Blob([view], { type: "audio/mpeg" });
        const url = URL.createObjectURL(blob);
        setAudioWithVocalsSrc(url);
      } else if (selected) {
        alert("Please upload a valid MP3 file.");
      }
    } catch (err) {
      console.error("Error selecting audio with vocals file:", err);
    }
  };

  const handleOutputDirectoryChange = async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
      });

      if (typeof selected === "string") {
        setOutputDirectory(selected);
      }
    } catch (err) {
      console.error("Error selecting directory:", err);
    }
  };

  const handleFormReset = () => {
    setTimeSignature("");
    setSwing(false);
    setGenre("");
    setSource("");
    setPitch("");
    setScale("");
    setTempo("");
    setAnnotator("");
    setAudioFilePath("");
    setAudioWithVocalsFilePath("");
    setMidiFilePath("");
    setProjectFilePath("");
    setAudioSrc("");
    setAudioWithVocalsSrc("");
    setOutputDirectory("");
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoaderOpen(true);
    setLoaderMessage("Submitting...");
    setLoaderProgress(15);
    try {
      setLoaderMessage("Uploading your files to cloud...");
      setLoaderProgress(40);

      const payload = {
        swing,
        time_signature,
        genre,
        source,
        pitch,
        scale,
        tempo,
        annotator,
        audio_path: audioFilePath,
        audio_with_vocals_path: audioWithVocalsFilePath,
        midi_zip_path: midiFilePath,
        project_zip_path: projectFilePath,
        project_file_name: projectFilePath.split(/[/\\]/).pop() || "",
        output_directory: outputDirectory,
      };

      console.log("STYLE UPLOAD PAYLOAD:", payload);
      setLoaderMessage("Final Save...");
      await StyleAPIService.uploadStyle(payload);
      setLoaderProgress(100);
      setIsLoaderOpen(false);
      setShowSuccessDialog(true);
    } catch (error: any) {
      console.error(error);
      alert(`Error uploading style: ${error.meesage || error}`);
      setIsLoaderOpen(false);
    }
  };

  const handleSuccessDialogClose = () => {
    handleFormReset();
    setShowSuccessDialog(false);
    commons.hardResfresh();
  };

  const showFormResetConfirmation = () => {
    setConfirmationDialogConfig({
      title: "Reset",
      message: "Are you sure you want to reset this style ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        handleFormReset();
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  const showSubmitConfirmation = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setConfirmationDialogConfig({
      title: "Confirm Submission",
      message: "Are you sure you want to submit this style ?",
      onConfirm: async () => {
        setIsConfirmationDialogOpen(false);
        await handleSubmit(event);
      },
    });
    setIsConfirmationDialogOpen(true);
  };

  useEffect(() => {
    const fetchGenreList = async () => {
      try {
        const genres: any[] = await StyleAPIService.getConfigGenres();
        setGenreList(genres);
        setIsFormFilled(checkFormFillStatus());
      } catch (error: any) {
        alert(`Error fetching genres: ${error.message}`);
      }
    };
    fetchGenreList();
  }, []);

  useEffect(() => {
    setIsFormFilled(checkFormFillStatus());
  }, [
    time_signature,
    genre,
    source,
    pitch,
    scale,
    tempo,
    annotator,
    audioFilePath,
    audioWithVocalsFilePath,
    midiFilePath,
    projectFilePath,
    outputDirectory,
  ]);

  // --------------- RENDER ---------------
  return (
    <>
      <FullScreenLoader isOpen={isLoaderOpen} message={loaderMessage} progress={loaderProgress} />
      <SuccessDialog
        isOpen={showSuccessDialog}
        message={"Your style has been uploaded successfully."}
        onConfirm={handleSuccessDialogClose}
      />
      <ConfirmationDialog
        isOpen={isConfirmationDialogOpen}
        title={confirmationDialogConfig.title}
        message={confirmationDialogConfig.message}
        onConfirm={confirmationDialogConfig.onConfirm}
        onCancel={() => setIsConfirmationDialogOpen(false)}
      />

      <p className="p-3 mt-3 font-semibold">Upload Style</p>
      <div className="w-[80%] mx-auto p-4 bg-gray-900">
        <form onSubmit={showSubmitConfirmation} className="space-y-6">
          {/* First Row: Annotator, Time Signature, and Swing */}
          <div className="grid grid-cols-12 gap-4">
            <div className="col-span-5 space-y-2">
              <label htmlFor="annotator" className="block mb-1 font-medium">
                Annotator <span className="text-red-500">*</span>
              </label>
              <select
                id="annotator"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                value={annotator}
                onChange={(e) => setAnnotator(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select annotator
                </option>
                {AnnotatorList.map((name, i) => (
                  <option key={i} value={name}>
                    {name}
                  </option>
                ))}
              </select>
            </div>

            <div className="col-span-5 space-y-2">
              <label htmlFor="timeSignature" className="block mb-1 font-medium">
                Time Signature <span className="text-red-500">*</span>
              </label>
              <select
                id="timeSignature"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                value={time_signature || ""}
                onChange={(e) => setTimeSignature(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select time signature
                </option>
                {TimeSignList.map((ts, i) => (
                  <option key={i} value={TimeSignKeyMap[ts]}>
                    {ts}
                  </option>
                ))}
              </select>
            </div>

            <div className="col-span-2 space-y-2">
              <div className="flex flex-col items-center">
                <label htmlFor="swing" className="block mb-1 font-medium">
                  Swing <span className="text-red-500">*</span>
                </label>
                <div className="h-[42px] flex items-center">
                  <button
                    type="button"
                    onClick={() => setSwing(!swing)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 ${
                      swing ? "bg-pink-600" : "bg-gray-600"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        swing ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Second Row: Output Directory */}
          <div className="space-y-2">
            <label htmlFor="outputDirectory" className="block mb-1 font-medium">
              Output Directory <span className="text-red-500">*</span>
            </label>
            <button
              type="button"
              onClick={handleOutputDirectoryChange}
              className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate"
            >
              <span className="flex items-center justify-between w-full">
                <span className="flex items-center gap-2">
                  <AiOutlineFolderOpen className="inline-block" fontSize={18} />
                  {outputDirectory ? outputDirectory : "Select Output Folder"}
                </span>
                <RxOpenInNewWindow className="ml-2 text-xl" />
              </span>
            </button>
          </div>

          {/* Third Row: Genre, Scale, Tempo, Source */}
          <div className="grid grid-cols-4 gap-4">
            <div className="space-y-2">
              <label htmlFor="genre" className="block mb-1 font-medium">
                Genre <span className="text-red-500">*</span>
              </label>
              <select
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                defaultValue={genre || ""}
                onChange={(e) => setGenre(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select a genre
                </option>
                {genreList.map((val, index) => (
                  <option key={index} value={val}>
                    {val}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="pitch" className="block mb-1 font-medium">
                Key <span className="text-red-500">*</span>
              </label>
              <select
                id="pitch"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                value={pitch || ""}
                onChange={(e) => setPitch(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select a key
                </option>
                {SongScaleList.map((sc, i) => (
                  <option key={i} value={SongScaleKeyMap[sc]}>
                    {sc}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="scale" className="block mb-1 font-medium">
                Scale <span className="text-red-500">*</span>
              </label>
              <select
                id="scale"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 appearance-none"
                value={scale || ""}
                onChange={(e) => setScale(e.target.value)}
                required
              >
                <option value="" disabled>
                  Select a scale
                </option>
                <option value="major">Major</option>
                <option value="minor">Minor</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="tempo" className="block mb-1 font-medium">
                Tempo <span className="text-red-500">*</span>
              </label>
              <input
                id="tempo"
                type="number"
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200"
                min={60}
                max={300}
                value={tempo}
                onChange={(e) => setTempo(e.target.value)}
                required
                placeholder="Enter tempo"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="source" className="block mb-1 font-medium">
                Source <span className="text-red-500">*</span>
              </label>
              <input
                id="source"
                type="text"
                value={source}
                onChange={(e) => setSource(e.target.value.trim())}
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200"
                placeholder="Enter song name"
                required
              />
            </div>
          </div>

          {/* Fourth Row: Project and MIDI Files */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="rppFile" className="block mb-1 font-medium">
                Project file (.zip) <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={handleProjectSelect}
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate"
              >
                <span className="flex items-center justify-between w-full">
                  <span className="flex items-center gap-2">
                    <AiOutlineFileZip className="inline-block" fontSize={16} />
                    {projectFilePath ? projectFilePath : "Browse a file"}
                  </span>
                  <RxOpenInNewWindow className="ml-2 text-xl" />
                </span>
              </button>
            </div>

            <div className="space-y-2">
              <label htmlFor="midiFile" className="block mb-1 font-medium">
                MIDI file (.zip) <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={handleMidiSelect}
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate"
              >
                <span className="flex items-center justify-between w-full">
                  <span className="flex items-center gap-2">
                    <AiOutlineFileZip className="inline-block" fontSize={16} />
                    {midiFilePath ? midiFilePath : "Browse a file"}
                  </span>
                  <RxOpenInNewWindow className="ml-2 text-xl" />
                </span>
              </button>
            </div>
          </div>

          {/* Fifth Row: Audio Files */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="audioFile" className="block mb-1 font-medium">
                Karaoke Audio (.mp3) <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={handleAudioSelect}
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate"
              >
                <span className="flex items-center justify-between w-full">
                  <span className="flex items-center gap-2">
                    <AiOutlineFileAdd className="inline-block" fontSize={16} />
                    {audioFilePath ? audioFilePath : "Browse a file"}
                  </span>
                  <RxOpenInNewWindow className="ml-2 text-xl" />
                </span>
              </button>
            </div>

            <div className="space-y-2">
              <label htmlFor="audioWithVocalsFile" className="block mb-1 font-medium">
                Audio with Vocals (.mp3) <span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={handleAudioWithVocalsSelect}
                className="border border-gray-700 bg-gray-800 rounded-md p-2 text-sm w-full text-gray-200 hover:bg-gray-700 truncate"
              >
                <span className="flex items-center justify-between w-full">
                  <span className="flex items-center gap-2">
                    <AiOutlineFileAdd className="inline-block" fontSize={16} />
                    {audioWithVocalsFilePath ? audioWithVocalsFilePath : "Browse a file"}
                  </span>
                  <RxOpenInNewWindow className="ml-2 text-xl" />
                </span>
              </button>
            </div>
          </div>

          {/* Sixth Row: Audio Players */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <AudioPlayer src={audioSrc} label="Karaoke Audio" />
            </div>

            <div className="space-y-2">
              <AudioPlayer src={audioWithVocalsSrc} label="Audio with Vocals" />
            </div>
          </div>

          {/* Seventh Row: Submit and Reset Buttons */}
          <div className="grid grid-cols-4 gap-4">
            <div className="col-span-3">
              <button
                type="submit"
                disabled={!isFormFilled}
                className="w-full py-3 bg-pink-600 text-white rounded-md hover:bg-pink-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Upload Style
              </button>
            </div>
            <div>
              <button
                type="reset"
                className="w-full py-3 bg-gray-600 text-white font-semibold rounded-md hover:bg-gray-700 transition duration-300"
                onClick={(e) => {
                  e.preventDefault();
                  showFormResetConfirmation();
                }}
              >
                Reset
              </button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default StyleUpload;
