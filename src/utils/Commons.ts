class Commons {
  hardResfresh() {
    window.location.reload();
  }

  sleep = async (ms: number): Promise<void> => {
    if (ms > 0) await new Promise((f) => setTimeout(f, ms));
  };

  removeUndefinedProps(obj: any) {
    for (let prop in obj) {
      if (obj.hasOwnProperty(prop) && obj[prop] === undefined) {
        delete obj[prop];
      }
    }
  }

  removeFalsyProps(obj: any) {
    for (let prop in obj) {
      if (
        obj.hasOwnProperty(prop) &&
        (obj[prop] === undefined || obj[prop] === null || obj[prop] === "")
      ) {
        delete obj[prop];
      }
    }
  }

  getFileNameAndExtension(filePath: string): { path: string; ext: string } {
    const fileName = filePath.split(/[/\\]/).pop() || ""; // handles both / and \\
    const lastDot = fileName.lastIndexOf(".");

    if (lastDot === -1) {
      return { path: fileName, ext: "" }; // no extension
    }

    return {
      path: fileName.substring(0, lastDot),
      ext: fileName.substring(lastDot + 1),
    };
  }
}

const commons = new Commons();

export default commons;
