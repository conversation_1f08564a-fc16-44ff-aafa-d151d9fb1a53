// import { invoke } from "@tauri-apps/api/core"

// let serverStarted = false; // Singleton flag

// export async function startBackendServer() {
//     try {
//         console.log("IS BACKEND SERVER STARTED", serverStarted)
//         if (serverStarted) {
//             console.log("Backend server already started.........");
//             return true;
//         }
//         const resp = await invoke('start_fastapi')
//         console.log("Backend server started successfully::::::", resp);
//         serverStarted = true
//         return true
//     } catch (error: any) {
//         console.error("Error starting backend:", error);
//         throw new error
//     }
// }