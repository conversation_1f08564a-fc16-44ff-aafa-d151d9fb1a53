// utils/websocketService.ts
import { v4 as uuidv4 } from 'uuid';

type MessageHandler = (message: any) => void;
type StatusHandler = (status: 'connected' | 'disconnected' | 'error', error?: Error) => void;

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

class WebSocketService {
  private socket: WebSocket | null = null;
  private clientId: string;
  private messageHandlers: Map<string, Set<MessageHandler>> = new Map();
  private statusHandlers: Set<StatusHandler> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private url: string;
  private connectionPromise: Promise<void> | null = null; // To prevent multiple concurrent connections
  private isConnected: boolean = false;

  constructor() {
    // Generate a unique client ID or retrieve from storage if exists
    this.clientId = this.getClientId();
    this.url = `ws://localhost:8009/ws/chat/${this.clientId}`;
  }

  /**
   * Get client ID from localStorage if available (browser) or generate a new one
   */
  private getClientId(): string {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined' && window.localStorage) {
      const storedId = window.localStorage.getItem('ws_client_id');
      if (storedId) {
        return storedId;
      }
      const newId = uuidv4();
      window.localStorage.setItem('ws_client_id', newId);
      return newId;
    }
    // For server-side rendering, generate a temporary ID
    return uuidv4();
  }

  /**
   * Connect to the WebSocket server
   */
  connect(): Promise<void> {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // We're in server-side rendering, resolve without connecting
      return Promise.resolve();
    }

    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        this.socket = new WebSocket(this.url);

        this.socket.onopen = () => {
          console.log('WebSocket connected');
          this.reconnectAttempts = 0;
          this.notifyStatusHandlers('connected');
          this.connectionPromise = null; // Clear the promise on successful connection
          resolve();
        };

        this.socket.onclose = () => {
          console.log('WebSocket disconnected');
          this.notifyStatusHandlers('disconnected');
          this.connectionPromise = null; // Clear the promise on close
          this.attemptReconnect();
        };

        this.socket.onerror = (event) => {
          console.error('WebSocket error:', event);
          // Convert Event to a proper Error object
          const errorObj = new Error('WebSocket connection error');
          this.notifyStatusHandlers('error', errorObj);
          this.connectionPromise = null; // Clear the promise on error
          reject(errorObj);
        };

        this.socket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log(`Received message from ${this.clientId}:`, message);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };
      } catch (error) {
        console.error('Error creating WebSocket:', error);
        this.connectionPromise = null; // Clear the promise if WebSocket creation fails
        reject(new Error('Failed to create WebSocket connection'));
      }
    });

    return this.connectionPromise;
  }
  
  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }
  
  /**
   * Send a message to the WebSocket server
   */
  sendMessage(message: WebSocketMessage): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.log('Cannot send WebSocket message during server-side rendering');
      return;
    }
    
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      this.connect().then(() => {
        if (this.socket?.readyState === WebSocket.OPEN) {
          this.socket.send(JSON.stringify(message));
        }
      }).catch(error => {
        console.error('Failed to connect before sending message:', error);
      });
      return;
    }
    
    this.socket.send(JSON.stringify(message));
  }
  
  /**
   * Register a handler for a specific message type
   */
  onMessage(type: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set());
    }
    
    this.messageHandlers.get(type)?.add(handler);
    
    // Return a function to unregister this handler
    return () => {
      this.messageHandlers.get(type)?.delete(handler);
    };
  }
  
  /**
   * Register a handler for connection status changes
   */
  onStatusChange(handler: StatusHandler): () => void {
    console.log("Adding status handler");
    this.statusHandlers.add(handler);
    
    // Return a function to unregister this handler
    return () => {
      console.log("Removing status handler");
      this.statusHandlers.delete(handler);
    };
  }
  
  /**
   * Handle incoming messages and dispatch to registered handlers
   */
  private handleMessage(message: WebSocketMessage): void {
    // Handle all message types
    if (this.messageHandlers.has('*')) {
      this.messageHandlers.get('*')?.forEach(handler => handler(message));
    }
    
    // Handle specific message type
    if (message.type && this.messageHandlers.has(message.type)) {
      this.messageHandlers.get(message.type)?.forEach(handler => handler(message));
    }
  }
  
  /**
   * Notify all status handlers of a status change
   */
  private notifyStatusHandlers(status: 'connected' | 'disconnected' | 'error', error?: Error): void {
    console.log(`Notifying status handlers: ${status}. Number of handlers: ${this.statusHandlers.size}`);
    this.statusHandlers.forEach(handler => {
      console.log("Executing status handler");
      handler(status, error);
    });
    this.isConnected = status === 'connected';
  }
  
  public getIsConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Attempt to reconnect to the WebSocket server with exponential backoff
   */
  private attemptReconnect(): void {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }
    
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached');
      return;
    }
    
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    console.log(`Attempting to reconnect in ${delay}ms...`);
    
    this.reconnectTimeout = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect().catch(() => {
        // If connection fails, attemptReconnect will be called again by onclose
      });
    }, delay);
  }
  
  // Chat-specific methods
  
  /**
   * Send a chat message to the server
   */
  sendChatMessage(content: string, streaming: boolean = true): void {
    this.sendMessage({
      type: 'chat_message',
      content,
      streaming
    });
  }
  
  /**
   * Upload a file for chat analysis
   */
  uploadFile(projectFilepath: string, midiFilepath?: string, outputDir?: string): void {
    this.sendMessage({
      type: 'file_upload',
      project_filepath: projectFilepath,
      midi_filepath: midiFilepath || '',
      output_dir: outputDir || ''
    });
  }
  
  /**
   * Reset the chat conversation
   */
  resetChat(): void {
    this.sendMessage({
      type: 'reset'
    });
  }
}

// Create a singleton instance
const websocketService = new WebSocketService();

export default websocketService;