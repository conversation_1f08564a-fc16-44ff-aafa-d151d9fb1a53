import os
import sys
import json
import platform

def update_tauri_config():
    # Path to the tauri.conf.json file
    config_path = os.path.join(os.getcwd(),'src-tauri/tauri.conf.json') 
    
    # Read the existing configuration
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Determine the current platform
    current_os = platform.system().lower()
    
    # Base path to the backend dist directory
    backend_dist_path = '../backend/dist'
    
    # Update resources based on the current OS
    if current_os == 'darwin':  # macOS
        config['bundle']['resources'] = [
            os.path.join(backend_dist_path, 'fastapi_app'),
        ]
    elif current_os == 'windows':
        config['bundle']['resources'] = [
            os.path.join(backend_dist_path, 'fastapi_app.exe'),
        ]
    else:  # Linux or other platforms
        config['bundle']['resources'] = [
            os.path.join(backend_dist_path, 'fastapi_app'),
        ]
    
    # Write the updated configuration back to the file
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Updated Tauri config for {current_os}")

def main():
    try:
        update_tauri_config()
    except Exception as e:
        print(f"Error updating Tauri config: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()